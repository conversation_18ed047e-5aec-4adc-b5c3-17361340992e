{"doc": " 知识库文档视图对象 app_knowledge_document\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 文档ID\n"}, {"name": "knowledgeBaseId", "doc": " 知识库ID\n"}, {"name": "title", "doc": " 文档标题\n"}, {"name": "content", "doc": " 文档内容\n"}, {"name": "docType", "doc": " 文档类型 (text/pdf/word/markdown/etc.)\n"}, {"name": "source", "doc": " 文档来源 (upload/url/api/etc.)\n"}, {"name": "originalFilename", "doc": " 原始文件名\n"}, {"name": "filePath", "doc": " 文件路径\n"}, {"name": "fileSize", "doc": " 文件大小 (字节)\n"}, {"name": "status", "doc": " 文档状态 (0=处理中 1=已完成 2=失败)\n"}, {"name": "processStatus", "doc": " 处理状态 (0=未处理 1=已向量化 2=已索引)\n"}, {"name": "vectorCount", "doc": " 向量数量\n"}, {"name": "summary", "doc": " 文档摘要\n"}, {"name": "tags", "doc": " 文档标签 (JSON数组)\n"}, {"name": "metadata", "doc": " 文档元数据 (JSON格式)\n"}, {"name": "processConfig", "doc": " 处理配置 (JSON格式)\n"}, {"name": "errorMessage", "doc": " 错误信息\n"}, {"name": "lastProcessTime", "doc": " 最后处理时间\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createDept", "doc": " 创建部门\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateBy", "doc": " 更新者\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "knowledgeBaseName", "doc": " 知识库名称\n"}, {"name": "docTypeName", "doc": " 文档类型名称（用于显示）\n"}, {"name": "sourceName", "doc": " 文档来源名称（用于显示）\n"}, {"name": "statusName", "doc": " 状态名称（用于显示）\n"}, {"name": "processStatusName", "doc": " 处理状态名称（用于显示）\n"}, {"name": "createByName", "doc": " 创建者名称\n"}, {"name": "updateByName", "doc": " 更新者名称\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "fileSizeFormatted", "doc": " 文件大小（格式化显示）\n"}, {"name": "processProgress", "doc": " 处理进度百分比\n"}, {"name": "avgSimilarity", "doc": " 平均向量相似度\n"}], "enumConstants": [], "methods": [], "constructors": []}