{"doc": " 书籍阅读记录Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据用户ID和书籍ID查询阅读记录\n\n @param userId 用户ID\n @param bookId 书籍ID\n @return 阅读记录\n"}, {"name": "selectUserReadingHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long"], "doc": " 查询用户的阅读历史（分页）\n\n @param page   分页参数\n @param userId 用户ID\n @return 阅读记录列表\n"}, {"name": "selectRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近阅读的书籍\n\n @param userId 用户ID\n @param limit  限制数量\n @return 阅读记录列表\n"}, {"name": "selectUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": " 统计用户阅读数据\n\n @param userId 用户ID\n @return 阅读统计数据\n"}, {"name": "insertOrUpdate", "paramTypes": ["org.dromara.app.domain.BookReadingRecord"], "doc": " 更新或插入阅读记录\n\n @param record 阅读记录\n @return 影响行数\n"}], "constructors": []}