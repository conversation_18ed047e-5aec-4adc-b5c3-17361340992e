{"doc": " 结果分享记录对象 app_result_share\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [{"name": "id", "doc": " 分享记录ID\n"}, {"name": "resultId", "doc": " 结果ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "platform", "doc": " 分享平台（wechat,qq,weibo,link）\n"}, {"name": "shareUrl", "doc": " 分享链接\n"}, {"name": "content", "doc": " 分享内容\n"}, {"name": "viewCount", "doc": " 查看次数\n"}, {"name": "expiresAt", "doc": " 过期时间\n"}, {"name": "status", "doc": " 状态（active,expired,disabled）\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}