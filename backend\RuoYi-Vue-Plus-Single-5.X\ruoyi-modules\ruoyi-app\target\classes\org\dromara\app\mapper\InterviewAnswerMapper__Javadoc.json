{"doc": " 面试答案Mapper接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": " 根据会话ID查询所有答案\n\n @param sessionId 会话ID\n @return 答案列表\n"}, {"name": "selectBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据会话ID和问题ID查询答案\n\n @param sessionId  会话ID\n @param questionId 问题ID\n @return 答案\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID查询最近的答案\n\n @param userId 用户ID\n @param limit  限制数量\n @return 答案列表\n"}, {"name": "countAnsweredBySessionId", "paramTypes": ["java.lang.String"], "doc": " 统计会话已回答问题数量\n\n @param sessionId 会话ID\n @return 已回答数量\n"}, {"name": "countSkippedBySessionId", "paramTypes": ["java.lang.String"], "doc": " 统计会话跳过问题数量\n\n @param sessionId 会话ID\n @return 跳过数量\n"}, {"name": "selectAvgScoreBySessionId", "paramTypes": ["java.lang.String"], "doc": " 查询会话的平均分数\n\n @param sessionId 会话ID\n @return 平均分数\n"}, {"name": "selectBySessionIdAndStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据状态查询答案列表\n\n @param sessionId 会话ID\n @param status    状态\n @return 答案列表\n"}], "constructors": []}