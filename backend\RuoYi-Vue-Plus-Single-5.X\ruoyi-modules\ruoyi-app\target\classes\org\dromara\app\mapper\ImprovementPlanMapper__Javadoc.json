{"doc": " 提升计划Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询提升计划\n\n @param resultId 结果ID\n @return 提升计划\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询提升计划列表\n\n @param userId 用户ID\n @return 提升计划列表\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和状态查询提升计划列表\n\n @param userId 用户ID\n @param status 状态\n @return 提升计划列表\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除提升计划\n\n @param resultId 结果ID\n @return 删除数量\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新计划进度\n\n @param id       计划ID\n @param progress 进度\n @return 更新数量\n"}, {"name": "selectActiveByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户活跃的提升计划\n\n @param userId 用户ID\n @return 提升计划列表\n"}, {"name": "selectExpiringPlans", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询即将到期的提升计划\n\n @param userId 用户ID\n @param days   天数\n @return 提升计划列表\n"}], "constructors": []}