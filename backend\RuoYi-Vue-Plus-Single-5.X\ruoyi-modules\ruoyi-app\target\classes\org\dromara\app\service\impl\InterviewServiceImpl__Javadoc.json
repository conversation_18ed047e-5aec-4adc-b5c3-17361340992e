{"doc": " 面试服务实现类\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "calculateRemainingTime", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": " 计算剩余时间（秒）\n"}, {"name": "getScoreLevel", "paramTypes": ["java.lang.Double"], "doc": " 根据分数获取等级\n"}, {"name": "generateAndSaveSessionQuestions", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": " 生成并保存会话问题\n"}, {"name": "createEmptyHistoryResponse", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 创建空的历史记录响应\n"}, {"name": "batchConvertToInterviewRecords", "paramTypes": ["java.util.List"], "doc": " 批量转换InterviewResult为InterviewRecord，优化数据库查询\n"}, {"name": "convertToInterviewRecordOptimized", "paramTypes": ["org.dromara.app.domain.InterviewResult", "java.util.Map", "java.util.Map"], "doc": " 转换InterviewResult为InterviewRecord（优化版本）\n"}, {"name": "getJobIconByCategory", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 根据分类ID获取岗位图标（优化版本）\n"}, {"name": "convertToInterviewRecord", "paramTypes": ["org.dromara.app.domain.InterviewResult"], "doc": " 转换InterviewResult为InterviewRecord（兼容旧版本）\n"}, {"name": "getDifficultyText", "paramTypes": ["java.lang.Integer"], "doc": " 根据分数获取难度文本\n"}, {"name": "convertStatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 转换状态\n"}, {"name": "getTimeAgo", "paramTypes": ["java.time.LocalDateTime"], "doc": " 获取时间差描述\n"}, {"name": "getJobIcon", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位图标\n"}, {"name": "getJobCategory", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位分类\n"}, {"name": "createInterviewStep", "paramTypes": ["int", "java.lang.String", "int", "java.lang.String"], "doc": " 创建面试流程步骤\n"}, {"name": "createSkillPoint", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": " 创建技能考查点\n"}], "constructors": []}