package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__80;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__80.class,
    uses = {InterviewModeToInterviewModeVoMapper.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper extends BaseMapper<InterviewModeVo, InterviewMode> {
}
