{"doc": " 应用用户对象 app_user\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "phone", "doc": " 用户手机号\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "realName", "doc": " 用户姓名\n"}, {"name": "gender", "doc": " 用户性别（男/女）\n"}, {"name": "studentId", "doc": " 学生学号\n"}, {"name": "major", "doc": " 专业\n"}, {"name": "grade", "doc": " 年级\n"}, {"name": "school", "doc": " 学校名称\n"}, {"name": "introduction", "doc": " 个人简介\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "avatar", "doc": " 用户头像\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}, {"name": "loginIp", "doc": " 最后登录IP\n"}, {"name": "loginDate", "doc": " 最后登录时间\n"}, {"name": "registeredAt", "doc": " 注册时间\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}