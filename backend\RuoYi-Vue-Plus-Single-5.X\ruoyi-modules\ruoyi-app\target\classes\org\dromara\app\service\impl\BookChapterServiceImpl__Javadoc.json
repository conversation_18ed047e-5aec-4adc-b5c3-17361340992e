{"doc": " 书籍章节Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryChaptersByBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据书籍ID查询章节列表\n"}, {"name": "query<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据章节ID查询章节内容\n"}, {"name": "queryChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Long"], "doc": " 根据书籍ID和章节序号查询章节\n"}, {"name": "queryPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": " 查询试读章节列表\n"}, {"name": "insertChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 新增章节\n"}, {"name": "updateChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 修改章节\n"}, {"name": "deleteChapter", "paramTypes": ["java.lang.String"], "doc": " 删除章节\n"}, {"name": "deleteChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID删除所有章节\n"}, {"name": "checkChapterAccess", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查用户是否有权限访问章节\n"}, {"name": "updateChapterUnlockStatus", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 更新章节解锁状态\n"}, {"name": "setUserChapterStatus", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Long"], "doc": " 设置用户章节状态（是否已完成等）\n"}, {"name": "calculateChapterStats", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 计算章节统计信息（字数、阅读时间）\n"}, {"name": "updateBookChapterCount", "paramTypes": ["java.lang.Long"], "doc": " 更新书籍的章节数\n"}], "constructors": []}