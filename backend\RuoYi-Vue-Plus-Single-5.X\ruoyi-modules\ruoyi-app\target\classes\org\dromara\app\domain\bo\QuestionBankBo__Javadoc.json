{"doc": " 题库业务对象 question_bank\n\n <AUTHOR>\n", "fields": [{"name": "bankId", "doc": " 题库ID\n"}, {"name": "bankCode", "doc": " 题库编码\n"}, {"name": "title", "doc": " 题库标题\n"}, {"name": "description", "doc": " 题库描述\n"}, {"name": "majorId", "doc": " 专业ID\n"}, {"name": "icon", "doc": " 题库图标\n"}, {"name": "color", "doc": " 题库颜色\n"}, {"name": "difficulty", "doc": " 难度（1-简单 2-中等 3-困难）\n"}, {"name": "totalQuestions", "doc": " 题目总数\n"}, {"name": "practiceCount", "doc": " 练习次数\n"}, {"name": "categories", "doc": " 分类标签（JSON格式）\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "keyword", "doc": " 查询关键词（用于搜索）\n"}, {"name": "majorName", "doc": " 专业名称（用于查询）\n"}, {"name": "statusArray", "doc": " 状态数组（用于批量查询）\n"}, {"name": "difficultyArray", "doc": " 难度数组（用于批量查询）\n"}, {"name": "createTimeStart", "doc": " 创建时间范围查询-开始时间\n"}, {"name": "createTimeEnd", "doc": " 创建时间范围查询-结束时间\n"}, {"name": "orderByColumn", "doc": " 排序字段\n"}, {"name": "isAsc", "doc": " 排序方向\n"}], "enumConstants": [], "methods": [], "constructors": []}