{"doc": " 面试结果视图对象 app_interview_result\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [{"name": "id", "doc": " 结果ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "jobId", "doc": " 岗位ID\n"}, {"name": "job<PERSON>ame", "doc": " 岗位名称\n"}, {"name": "company", "doc": " 公司名称\n"}, {"name": "mode", "doc": " 面试模式\n"}, {"name": "date", "doc": " 面试日期\n"}, {"name": "duration", "doc": " 面试时长\n"}, {"name": "totalScore", "doc": " 总分\n"}, {"name": "rank", "doc": " 等级（excellent,good,average,poor）\n"}, {"name": "rankText", "doc": " 等级文本\n"}, {"name": "percentile", "doc": " 百分位数\n"}, {"name": "answeredQuestions", "doc": " 已回答问题数\n"}, {"name": "totalQuestions", "doc": " 总问题数\n"}, {"name": "status", "doc": " 状态（completed,partial,cancelled）\n"}, {"name": "topStrengths", "doc": " 主要优势（JSON数组）\n"}, {"name": "topWeaknesses", "doc": " 主要劣势（JSON数组）\n"}, {"name": "overallFeedback", "doc": " 总体反馈\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}