{"doc": " 用户简历视图对象 app_user_resume\n\n <AUTHOR>\n", "fields": [{"name": "resumeId", "doc": " 简历主键\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "resumeName", "doc": " 简历名称\n"}, {"name": "originalName", "doc": " 原始文件名\n"}, {"name": "filePath", "doc": " 文件路径\n"}, {"name": "fileUrl", "doc": " 文件URL地址\n"}, {"name": "fileSize", "doc": " 文件大小(字节)\n"}, {"name": "fileSizeStr", "doc": " 文件大小(格式化后)\n"}, {"name": "fileSuffix", "doc": " 文件后缀名\n"}, {"name": "isDefault", "doc": " 是否默认简历(0否 1是)\n"}, {"name": "status", "doc": " 状态(0正常 1停用)\n"}, {"name": "ossId", "doc": " 对象存储ID\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "createBy", "doc": " 创建人\n"}, {"name": "createByName", "doc": " 创建人名称\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}