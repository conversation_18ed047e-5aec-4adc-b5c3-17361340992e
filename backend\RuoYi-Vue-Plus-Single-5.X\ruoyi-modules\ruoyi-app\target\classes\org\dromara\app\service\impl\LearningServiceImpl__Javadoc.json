{"doc": " 学习资源Service实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": " 构建查询条件\n"}, {"name": "convertToVo", "paramTypes": ["org.dromara.app.domain.QuestionBank", "java.lang.Long"], "doc": " 转换为VO对象\n"}, {"name": "convertQuestionToVo", "paramTypes": ["org.dromara.app.domain.Question"], "doc": " 转换题目为VO对象\n\n @param question 题目实体\n @return 题目VO\n"}, {"name": "convertCommentToVO", "paramTypes": ["org.dromara.app.domain.QuestionComment", "java.lang.Long"], "doc": " 转换评论实体为VO对象\n\n @param comment       评论实体\n @param currentUserId 当前用户ID\n @return 评论VO\n"}, {"name": "formatTimeDisplay", "paramTypes": ["java.util.Date"], "doc": " 格式化时间显示\n\n @param createTime 创建时间\n @return 格式化后的时间字符串\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取题库题目统计信息\n\n @param bankId 题库ID\n @return 统计信息\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": " 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\n\n @param queryDto 查询参数\n @param userId   用户ID\n @return 题库列表\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取专业题库统计信息\n\n @param majorId 专业ID\n @param userId  用户ID\n @return 统计信息\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取专业题库筛选选项计数\n\n @param majorId 专业ID\n @param userId  用户ID\n @return 筛选选项计数\n"}, {"name": "buildEnhancedQueryWrapper", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": " 构建增强的查询条件\n\n @param queryDto 查询参数\n @param userId   用户ID\n @return 查询条件包装器\n"}, {"name": "applyEnhancedFilter", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper", "java.lang.String", "java.lang.Long"], "doc": " 应用增强的筛选条件\n\n @param wrapper 查询条件包装器\n @param filter  筛选条件\n @param userId  用户ID\n"}, {"name": "applySorting", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper", "java.lang.String"], "doc": " 应用排序条件\n\n @param wrapper  查询条件包装器\n @param sortType 排序类型\n"}, {"name": "calculateTotalStudyHours", "paramTypes": ["java.lang.Long"], "doc": " 计算用户总学习时长（小时）\n"}, {"name": "calculateCompletedCourses", "paramTypes": ["java.lang.Long"], "doc": " 计算用户完成的课程数\n"}, {"name": "calculateCurrentStreak", "paramTypes": ["java.lang.Long"], "doc": " 计算连续学习天数\n"}, {"name": "calculateWeeklyProgress", "paramTypes": ["java.lang.Long"], "doc": " 计算本周学习进度\n"}, {"name": "findRecommendedQuestionBank", "paramTypes": ["java.lang.Long"], "doc": " 查找推荐的题库\n"}, {"name": "calculateBankProgress", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 计算题库学习进度\n"}, {"name": "calculateBookCount", "paramTypes": ["java.lang.String"], "doc": " 计算书籍数量\n"}, {"name": "calculateVideoCount", "paramTypes": ["java.lang.String"], "doc": " 计算视频数量\n"}], "constructors": []}