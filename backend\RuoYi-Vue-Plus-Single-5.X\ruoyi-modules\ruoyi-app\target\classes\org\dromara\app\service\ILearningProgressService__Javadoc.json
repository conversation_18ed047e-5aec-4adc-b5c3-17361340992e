{"doc": " 学习进度服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "startLearning", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long"], "doc": " 开始学习\n\n @param userId 用户ID\n @param learningPathId 学习路径ID\n @param resourceId 资源ID\n @return 学习进度\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 更新学习进度\n\n @param progressId 进度ID\n @param completionPercentage 完成百分比\n @param studyMinutes 学习时长\n @return 是否成功\n"}, {"name": "completeLearning", "paramTypes": ["java.lang.Long", "java.lang.Double", "java.lang.Double", "java.lang.String"], "doc": " 完成学习\n\n @param progressId 进度ID\n @param effectivenessRating 效果评分\n @param satisfactionRating 满意度评分\n @param notes 学习笔记\n @return 是否成功\n"}, {"name": "pauseLearning", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 暂停学习\n\n @param progressId 进度ID\n @param reason 暂停原因\n @return 是否成功\n"}, {"name": "resumeLearning", "paramTypes": ["java.lang.Long"], "doc": " 恢复学习\n\n @param progressId 进度ID\n @return 是否成功\n"}, {"name": "getUserLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取用户学习进度列表\n\n @param userId 用户ID\n @param status 状态筛选\n @return 学习进度列表\n"}, {"name": "getLearningProgressPage", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 分页查询学习进度\n\n @param userId 用户ID\n @param pageNum 页码\n @param pageSize 每页大小\n @param status 状态筛选\n @return 分页结果\n"}, {"name": "getLearningProgressDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取学习进度详情\n\n @param progressId 进度ID\n @param userId 用户ID\n @return 学习进度\n"}, {"name": "getProgressByLearningPath", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据学习路径获取进度\n\n @param learningPathId 学习路径ID\n @param userId 用户ID\n @return 学习进度\n"}, {"name": "getProgressByResource", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据资源获取进度\n\n @param resourceId 资源ID\n @param userId 用户ID\n @return 学习进度\n"}, {"name": "getUserLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取用户学习统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "getLearningEffectivenessAssessment", "paramTypes": ["java.lang.Long"], "doc": " 获取学习效果评估\n\n @param userId 用户ID\n @return 效果评估\n"}, {"name": "getStudyTimeStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取学习时间统计\n\n @param userId 用户ID\n @return 时间统计\n"}, {"name": "getLearningTrend", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取学习趋势\n\n @param userId 用户ID\n @param days 天数\n @return 趋势数据\n"}, {"name": "getRecentLearningRecords", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取最近学习记录\n\n @param userId 用户ID\n @param limit 限制数量\n @return 学习记录列表\n"}, {"name": "getOngoingLearning", "paramTypes": ["java.lang.Long"], "doc": " 获取正在进行的学习\n\n @param userId 用户ID\n @return 学习进度列表\n"}, {"name": "getCompletedLearning", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取已完成的学习\n\n @param userId 用户ID\n @param limit 限制数量\n @return 学习进度列表\n"}, {"name": "getOverdueLearning", "paramTypes": ["java.lang.Long"], "doc": " 获取超期学习项目\n\n @param userId 用户ID\n @return 超期学习列表\n"}, {"name": "createLearningPlan", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningPlan"], "doc": " 创建学习计划\n\n @param progressId 进度ID\n @param learningPlan 学习计划\n @return 是否成功\n"}, {"name": "updateLearningPlan", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningPlan"], "doc": " 更新学习计划\n\n @param progressId 进度ID\n @param learningPlan 学习计划\n @return 是否成功\n"}, {"name": "addLearningFeedback", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningFeedback"], "doc": " 添加学习反馈\n\n @param progressId 进度ID\n @param feedback 学习反馈\n @return 是否成功\n"}, {"name": "updateMilestoneStatus", "paramTypes": ["java.lang.Long", "java.lang.String", "boolean"], "doc": " 更新里程碑完成状态\n\n @param progressId 进度ID\n @param milestone 里程碑名称\n @param completed 是否完成\n @return 是否成功\n"}, {"name": "calculateLearningEfficiency", "paramTypes": ["java.lang.Long"], "doc": " 计算学习效率\n\n @param progressId 进度ID\n @return 学习效率评分\n"}, {"name": "generateLearningReport", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 生成学习报告\n\n @param userId 用户ID\n @param startDate 开始日期\n @param endDate 结束日期\n @return 学习报告\n"}, {"name": "recommendLearningAdjustments", "paramTypes": ["java.lang.Long"], "doc": " 推荐学习内容调整\n\n @param userId 用户ID\n @return 调整建议\n"}, {"name": "getLearningReminders", "paramTypes": ["java.lang.Long"], "doc": " 获取学习提醒列表\n\n @param userId 用户ID\n @return 提醒列表\n"}, {"name": "setLearningReminder", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.ReminderSettings"], "doc": " 设置学习提醒\n\n @param progressId 进度ID\n @param reminderSettings 提醒设置\n @return 是否成功\n"}, {"name": "batchUpdateLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": " 批量更新学习统计\n\n @param userId 用户ID\n @return 更新数量\n"}, {"name": "deleteLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除学习进度\n\n @param progressId 进度ID\n @param userId 用户ID\n @return 是否成功\n"}, {"name": "exportLearningData", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 导出学习数据\n\n @param userId 用户ID\n @param format 导出格式\n @return 导出文件路径\n"}, {"name": "analyzeLearningData", "paramTypes": ["java.lang.Long"], "doc": " 学习数据分析\n\n @param userId 用户ID\n @return 分析结果\n"}, {"name": "getLearningAchievements", "paramTypes": ["java.lang.Long"], "doc": " 获取学习成就\n\n @param userId 用户ID\n @return 成就列表\n"}, {"name": "getPersonalizedRecommendations", "paramTypes": ["java.lang.Long"], "doc": " 获取个性化推荐\n\n @param userId 用户ID\n @return 个性化推荐列表\n"}, {"name": "batchUpdateProgress", "paramTypes": ["java.util.List", "java.util.Map"], "doc": " 批量更新学习进度\n\n @param progressIds 进度ID列表\n @param updateData 更新数据\n @return 是否成功\n"}, {"name": "importLearningData", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 导入学习数据\n\n @param userId 用户ID\n @param importData 导入数据\n @return 是否成功\n"}, {"name": "getProgressComparison", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 获取学习进度对比\n\n @param userId 用户ID\n @param compareUserIds 对比用户ID列表\n @return 对比结果\n"}, {"name": "getLearningInsights", "paramTypes": ["java.lang.Long"], "doc": " 获取学习洞察\n\n @param userId 用户ID\n @return 学习洞察数据\n"}], "constructors": []}