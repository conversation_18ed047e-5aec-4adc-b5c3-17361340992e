{"doc": " 用户活动统计Mapper接口\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserAndTypeAndDate", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate"], "doc": " 根据用户ID、活动类型和日期查询统计记录\n\n @param userId       用户ID\n @param activityType 活动类型\n @param statDate     统计日期\n @return 统计记录\n"}, {"name": "selectByUserAndDateRange", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 查询用户指定日期范围内的统计记录\n\n @param userId       用户ID\n @param activityType 活动类型\n @param startDate    开始日期\n @param endDate      结束日期\n @return 统计记录列表\n"}, {"name": "selectTodayStatistics", "paramTypes": ["java.lang.Long"], "doc": " 查询用户今日各类型活动统计\n\n @param userId 用户ID\n @return 今日统计列表\n"}, {"name": "selectWeekStatistics", "paramTypes": ["java.lang.Long"], "doc": " 查询用户本周各类型活动统计汇总\n\n @param userId 用户ID\n @return 本周统计列表\n"}, {"name": "selectMonthStatistics", "paramTypes": ["java.lang.Long"], "doc": " 查询用户本月各类型活动统计汇总\n\n @param userId 用户ID\n @return 本月统计列表\n"}, {"name": "updateStatistics", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.lang.Long"], "doc": " 更新统计记录\n\n @param userId       用户ID\n @param activityType 活动类型\n @param statDate     统计日期\n @param duration     新增时长\n @return 更新行数\n"}, {"name": "deleteUserStatistics", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 删除用户指定类型的统计记录\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 删除行数\n"}], "constructors": []}