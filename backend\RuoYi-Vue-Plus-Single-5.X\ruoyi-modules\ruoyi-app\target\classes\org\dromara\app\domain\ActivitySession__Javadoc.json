{"doc": " 用户活动会话记录对象 app_activity_session\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "id", "doc": " 会话ID\n"}, {"name": "sessionId", "doc": " 会话唯一标识符\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "activityType", "doc": " 活动类型\n"}, {"name": "activityId", "doc": " 活动对象ID(如题目ID、课程ID等)\n"}, {"name": "activityName", "doc": " 活动名称\n"}, {"name": "categoryId", "doc": " 分类ID(如题库ID、课程分类ID等)\n"}, {"name": "categoryName", "doc": " 分类名称\n"}, {"name": "startTime", "doc": " 开始时间\n"}, {"name": "endTime", "doc": " 结束时间\n"}, {"name": "duration", "doc": " 持续时长(毫秒)\n"}, {"name": "isActive", "doc": " 是否活跃(1:活跃 0:已结束)\n"}, {"name": "metadata", "doc": " 额外元数据(JSON格式)\n"}], "enumConstants": [], "methods": [{"name": "calculateDuration", "paramTypes": [], "doc": " 计算持续时长\n 如果会话还在进行中，计算到当前时间的时长\n 如果会话已结束，返回记录的时长\n\n @return 持续时长(毫秒)\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 检查会话是否有效\n\n @return 是否有效\n"}, {"name": "endSession", "paramTypes": [], "doc": " 结束会话\n"}, {"name": "pauseSession", "paramTypes": [], "doc": " 暂停会话\n"}, {"name": "resumeSession", "paramTypes": [], "doc": " 恢复会话\n"}], "constructors": []}