{"doc": " 成就系统服务实现类\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkSingleAchievement", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 检查单个成就\n"}, {"name": "checkAchievementCondition", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 检查成就条件是否满足\n"}, {"name": "checkLoginAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查登录类成就\n"}, {"name": "checkVideoWatchAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查视频观看类成就\n"}, {"name": "checkCommentAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查评论类成就\n"}, {"name": "checkLikeAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查点赞类成就\n"}, {"name": "checkStudyTimeAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查学习时长类成就\n"}, {"name": "updateAchievementProgressInternal", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 更新成就进度\n"}, {"name": "getCurrentValue", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 获取当前数值\n"}, {"name": "getTargetValue", "paramTypes": ["java.util.Map"], "doc": " 获取目标数值\n"}, {"name": "completeAchievement", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 完成成就\n"}, {"name": "createUserAchievement", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo"], "doc": " 创建用户成就记录\n"}], "constructors": []}