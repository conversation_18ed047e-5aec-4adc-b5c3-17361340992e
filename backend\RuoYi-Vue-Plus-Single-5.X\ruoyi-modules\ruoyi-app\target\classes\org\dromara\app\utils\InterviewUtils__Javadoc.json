{"doc": " 面试系统工具类\n\n <AUTHOR> Assistant\n @date 2025-01-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateSessionId", "paramTypes": [], "doc": " 生成会话ID\n\n @return 会话ID\n"}, {"name": "generateSessionToken", "paramTypes": ["java.lang.String"], "doc": " 生成会话令牌\n\n @param sessionId 会话ID\n @return 会话令牌\n"}, {"name": "calculateExpirationTime", "paramTypes": ["int"], "doc": " 计算会话过期时间\n\n @param hours 过期小时数\n @return 过期时间\n"}, {"name": "isValidKeyword", "paramTypes": ["java.lang.String", "org.dromara.app.config.InterviewConfig"], "doc": " 验证搜索关键词\n\n @param keyword 关键词\n @param config  配置\n @return 是否有效\n"}, {"name": "calculateSmartScore", "paramTypes": ["org.dromara.app.domain.vo.JobVo", "org.dromara.app.config.InterviewConfig"], "doc": " 计算智能排序分数\n\n @param job    岗位信息\n @param config 配置\n @return 智能分数\n"}, {"name": "parsePassRate", "paramTypes": ["java.lang.String"], "doc": " 解析通过率字符串\n\n @param passRateStr 通过率字符串（如\"68%\"）\n @return 通过率数值\n"}, {"name": "formatPassRate", "paramTypes": ["double"], "doc": " 格式化通过率\n\n @param passRate 通过率数值\n @return 格式化后的字符串\n"}, {"name": "isSessionExpired", "paramTypes": ["java.time.LocalDateTime"], "doc": " 检查会话是否过期\n\n @param expiresAt 过期时间\n @return 是否过期\n"}, {"name": "isValidTags", "paramTypes": ["java.util.List"], "doc": " 验证岗位标签\n\n @param tags 标签列表\n @return 是否有效\n"}, {"name": "cleanKeyword", "paramTypes": ["java.lang.String"], "doc": " 清理和标准化关键词\n\n @param keyword 原始关键词\n @return 清理后的关键词\n"}, {"name": "simulate<PERSON>ev<PERSON><PERSON><PERSON>ck", "paramTypes": ["org.dromara.app.config.InterviewConfig"], "doc": " 生成设备检测结果（模拟）\n\n @param config 配置\n @return 检测结果\n"}, {"name": "isValidModeId", "paramTypes": ["java.lang.String"], "doc": " 验证面试模式ID\n\n @param modeId 模式ID\n @return 是否有效\n"}, {"name": "calculateRecommendScore", "paramTypes": ["org.dromara.app.domain.vo.JobVo"], "doc": " 计算推荐分数\n\n @param job 岗位信息\n @return 推荐分数\n"}, {"name": "getDifficultyDescription", "paramTypes": ["java.lang.Integer"], "doc": " 获取难度描述\n\n @param difficulty 难度等级\n @return 难度描述\n"}, {"name": "isValidCustomQuestions", "paramTypes": ["java.util.List"], "doc": " 验证自定义问题\n\n @param questions 问题列表\n @return 是否有效\n"}], "constructors": []}