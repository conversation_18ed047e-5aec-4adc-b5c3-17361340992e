{"doc": " 聊天消息对象 app_chat_message\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 消息ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "role", "doc": " 消息角色：user/assistant/system\n"}, {"name": "content", "doc": " 消息内容\n"}, {"name": "messageType", "doc": " 消息类型：text/image/file/voice\n"}, {"name": "attachments", "doc": " 附件信息（JSON格式）\n"}, {"name": "status", "doc": " 消息状态：0-发送中，1-发送成功，2-发送失败\n"}, {"name": "errorMessage", "doc": " 错误信息（如果发送失败）\n"}, {"name": "metadata", "doc": " 消息元数据（JSON格式，存储额外信息如模型名称、token消耗等）\n"}, {"name": "parentMessageId", "doc": " 父消息ID（用于消息回复链）\n"}, {"name": "messageIndex", "doc": " 消息序号（在会话中的顺序）\n"}, {"name": "isRead", "doc": " 是否已读\n"}, {"name": "attachmentList", "doc": " 附件列表（不存储到数据库，用于返回给前端）\n"}, {"name": "metadataObject", "doc": " 消息元数据对象（不存储到数据库）\n"}, {"name": "delFlag", "doc": " 删除标志（0-正常，1-删除）\n"}], "enumConstants": [], "methods": [], "constructors": []}