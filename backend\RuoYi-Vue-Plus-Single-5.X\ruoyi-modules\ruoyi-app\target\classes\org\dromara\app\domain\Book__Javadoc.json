{"doc": " 面试书籍对象 app_book\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 书籍ID\n"}, {"name": "title", "doc": " 书籍标题\n"}, {"name": "author", "doc": " 作者\n"}, {"name": "cover", "doc": " 封面图片URL\n"}, {"name": "category", "doc": " 分类：technical/behavioral/algorithm/resume/salary\n"}, {"name": "rating", "doc": " 评分\n"}, {"name": "readCount", "doc": " 阅读次数\n"}, {"name": "chapters", "doc": " 章节数\n"}, {"name": "pages", "doc": " 页数\n"}, {"name": "isCompleted", "doc": " 是否完结：0-连载中，1-已完结\n"}, {"name": "tags", "doc": " 标签，逗号分隔\n"}, {"name": "description", "doc": " 书籍描述\n"}, {"name": "difficulty", "doc": " 难度：入门/进阶/高级\n"}, {"name": "price", "doc": " 价格\n"}, {"name": "isFree", "doc": " 是否免费：0-付费，1-免费\n"}, {"name": "status", "doc": " 状态：0-下架，1-上架\n"}, {"name": "sortOrder", "doc": " 排序序号\n"}, {"name": "tagList", "doc": " 标签列表（转换后的字段，不存储到数据库）\n"}, {"name": "readingProgress", "doc": " 当前用户阅读进度（不存储到数据库）\n"}, {"name": "isPurchased", "doc": " 是否已购买（不存储到数据库）\n"}], "enumConstants": [], "methods": [], "constructors": []}