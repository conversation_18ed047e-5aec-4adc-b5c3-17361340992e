{"doc": " 视频映射工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "map", "paramTypes": ["java.lang.Integer"], "doc": " Integer转Boolean映射方法\n 0 -> false, 1 -> true\n\n @param value Integer值\n @return Boolean值\n"}, {"name": "map", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": " Boolean转Integer映射方法\n false -> 0, true -> 1\n\n @param value Boolean值\n @return Integer值\n"}, {"name": "map", "paramTypes": ["java.lang.String"], "doc": " String转List<String>映射方法\n 将JSON字符串转换为字符串列表\n\n @param value JSON字符串\n @return 字符串列表\n"}, {"name": "map", "paramTypes": ["java.util.List"], "doc": " List<String>转String映射方法\n 将字符串列表转换为JSON字符串\n\n @param value 字符串列表\n @return JSON字符串\n"}], "constructors": []}