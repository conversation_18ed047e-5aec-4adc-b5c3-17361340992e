{"doc": " 聊天响应DTO\n\n <AUTHOR>\n", "fields": [{"name": "success", "doc": " 是否成功\n"}, {"name": "message", "doc": " 响应消息\n"}, {"name": "messageId", "doc": " 消息ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "error", "doc": " 错误信息\n"}, {"name": "metadata", "doc": " 消息元数据\n"}], "enumConstants": [], "methods": [{"name": "success", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 创建成功响应\n"}, {"name": "error", "paramTypes": ["java.lang.String"], "doc": " 创建失败响应\n"}], "constructors": []}