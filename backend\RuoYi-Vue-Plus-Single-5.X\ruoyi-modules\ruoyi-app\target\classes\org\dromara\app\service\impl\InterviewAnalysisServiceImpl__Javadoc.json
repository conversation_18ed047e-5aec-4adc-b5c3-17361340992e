{"doc": " 面试分析服务实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateMockEmotionData", "paramTypes": [], "doc": " 生成模拟情绪数据（用于测试）\n"}, {"name": "findPrimaryEmotion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 找出主要情绪\n"}, {"name": "generateEmotionSuggestionText", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据情绪生成建议文本\n"}, {"name": "determineEmotionSuggestionLevel", "paramTypes": ["java.lang.String"], "doc": " 确定情绪建议的优先级\n"}, {"name": "buildSpeechSuggestionPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 构建语音建议的prompt\n"}, {"name": "buildComprehensiveSuggestionPrompt", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 构建综合建议的prompt\n"}, {"name": "parseSuggestionFromAI", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 从AI响应中解析建议\n"}, {"name": "createErrorResult", "paramTypes": ["java.lang.String"], "doc": " 创建错误结果\n"}], "constructors": []}