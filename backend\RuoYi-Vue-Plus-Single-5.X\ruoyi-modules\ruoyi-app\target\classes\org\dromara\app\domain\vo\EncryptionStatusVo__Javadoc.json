{"doc": " 加密状态视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "encryptionEnabled", "doc": " 加密服务是否启用\n"}, {"name": "currentAlgorithm", "doc": " 当前使用的加密算法\n"}, {"name": "keyVersion", "doc": " 密钥版本\n"}, {"name": "keyCreatedTime", "doc": " 密钥创建时间\n"}, {"name": "lastKeyRotationTime", "doc": " 密钥最后轮换时间\n"}, {"name": "nextKeyRotationTime", "doc": " 下次密钥轮换时间\n"}, {"name": "totalEncryptionCount", "doc": " 加密操作总数\n"}, {"name": "totalDecryptionCount", "doc": " 解密操作总数\n"}, {"name": "encryptionFailureCount", "doc": " 加密失败次数\n"}, {"name": "decryptionFailureCount", "doc": " 解密失败次数\n"}, {"name": "encryptionSuccessRate", "doc": " 加密成功率（百分比）\n"}, {"name": "decryptionSuccessRate", "doc": " 解密成功率（百分比）\n"}, {"name": "averageEncryptionTime", "doc": " 平均加密时间（毫秒）\n"}, {"name": "averageDecryptionTime", "doc": " 平均解密时间（毫秒）\n"}, {"name": "supportedAlgorithms", "doc": " 支持的算法列表\n"}, {"name": "encryptedDataStats", "doc": " 加密数据统计\n"}, {"name": "keyStrength", "doc": " 密钥强度等级：weak/medium/strong\n"}, {"name": "encryptionHealth", "doc": " 加密状态：healthy/warning/error\n"}, {"name": "securityRecommendations", "doc": " 安全建议\n"}, {"name": "lastCheckTime", "doc": " 最后检查时间\n"}, {"name": "needsKeyRotation", "doc": " 是否需要密钥轮换\n"}, {"name": "encryptionConfig", "doc": " 加密配置信息\n"}], "enumConstants": [], "methods": [], "constructors": []}