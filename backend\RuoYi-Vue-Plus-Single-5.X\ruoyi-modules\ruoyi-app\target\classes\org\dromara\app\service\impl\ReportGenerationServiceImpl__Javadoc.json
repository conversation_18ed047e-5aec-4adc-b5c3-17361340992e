{"doc": " 报告生成服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createMockAnalysisResult", "paramTypes": ["java.lang.String"], "doc": " 创建模拟分析结果\n"}, {"name": "createMockDimensionScore", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": " 创建模拟维度评分\n"}, {"name": "convertDimensionScores", "paramTypes": ["java.util.List"], "doc": " 转换维度评分格式\n"}, {"name": "generatePdfContent", "paramTypes": ["org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": " 生成PDF内容\n"}, {"name": "createImprovementSuggestion", "paramTypes": ["java.lang.String"], "doc": " 创建改进建议\n"}, {"name": "createDimensionImprovementSuggestion", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.DimensionScore"], "doc": " 基于维度评分创建改进建议\n"}, {"name": "createTechnicalLearningPath", "paramTypes": [], "doc": " 创建技术学习路径\n"}, {"name": "createCommunicationLearningPath", "paramTypes": [], "doc": " 创建沟通表达学习路径\n"}, {"name": "createLogicalThinkingLearningPath", "paramTypes": [], "doc": " 创建逻辑思维学习路径\n"}, {"name": "createInterviewSkillsLearningPath", "paramTypes": [], "doc": " 创建面试技巧学习路径\n"}, {"name": "createResource", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 创建学习资源\n"}, {"name": "getPriorityValue", "paramTypes": ["java.lang.String"], "doc": " 获取优先级数值\n"}], "constructors": []}