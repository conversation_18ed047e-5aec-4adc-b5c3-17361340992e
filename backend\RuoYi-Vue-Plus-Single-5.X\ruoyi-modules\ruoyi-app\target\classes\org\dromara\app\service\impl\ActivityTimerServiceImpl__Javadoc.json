{"doc": " 活动时长记录服务实现类\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "updateStatisticsAfterSession", "paramTypes": ["org.dromara.app.domain.ActivitySession"], "doc": " 更新会话结束后的统计数据\n\n @param session 活动会话\n"}, {"name": "ensureUserSummaryExists", "paramTypes": ["java.lang.Long"], "doc": " 确保用户活动总览记录存在\n\n @param userId 用户ID\n"}, {"name": "convertToSessionVO", "paramTypes": ["org.dromara.app.domain.ActivitySession"], "doc": " 转换ActivitySession为ActivitySessionVO\n\n @param session 活动会话\n @return 会话VO\n"}], "constructors": []}