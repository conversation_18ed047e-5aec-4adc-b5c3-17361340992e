{"doc": " AI代理Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectEnabledAgentsOrdered", "paramTypes": [], "doc": " 获取启用的代理列表（按排序号排序）\n\n @return 代理列表\n"}, {"name": "selectEnabledByType", "paramTypes": ["java.lang.String"], "doc": " 根据代理类型获取启用的代理\n\n @param agentType 代理类型\n @return 代理信息\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": " 增加代理使用次数\n\n @param agentType 代理类型\n @return 影响行数\n"}, {"name": "updateAverageRating", "paramTypes": ["java.lang.String", "java.lang.Double"], "doc": " 更新代理评分\n\n @param agentType     代理类型\n @param averageRating 平均评分\n @return 影响行数\n"}, {"name": "batchInsertDefaultAgents", "paramTypes": ["java.util.List"], "doc": " 批量初始化默认代理\n\n @param agents 代理列表\n @return 影响行数\n"}, {"name": "selectEnabledById", "paramTypes": ["java.lang.String"], "doc": " 根据代理ID获取启用的代理\n\n @param agentTypeId 代理类型ID\n @return 代理信息\n"}], "constructors": []}