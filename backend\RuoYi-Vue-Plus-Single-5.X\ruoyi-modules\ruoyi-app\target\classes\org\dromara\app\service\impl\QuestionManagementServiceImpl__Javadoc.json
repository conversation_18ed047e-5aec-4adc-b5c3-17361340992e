{"doc": " 问题管理增强Service业务层处理\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "createDifficultyLevel", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String", "java.util.List", "java.lang.Integer", "java.lang.Double"], "doc": " 创建难度级别\n"}, {"name": "identifyMissingAreas", "paramTypes": ["org.dromara.app.domain.Job", "java.util.List"], "doc": " 识别缺失领域\n"}, {"name": "getRequiredCategoriesForDomain", "paramTypes": ["java.lang.String"], "doc": " 获取技术领域必需的问题类型\n"}, {"name": "calculateCoverageScore", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 计算覆盖度分数\n"}], "constructors": []}