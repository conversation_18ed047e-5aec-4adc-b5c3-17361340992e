{"doc": " 音频指标Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询音频指标\n\n @param resultId 结果ID\n @return 音频指标\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除音频指标\n\n @param resultId 结果ID\n @return 删除数量\n"}, {"name": "selectHistoryByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户音频指标历史记录\n\n @param userId 用户ID\n @param limit  限制数量\n @return 音频指标列表\n"}, {"name": "selectAverageMetrics", "paramTypes": [], "doc": " 查询音频指标平均值\n\n @return 音频指标平均值\n"}, {"name": "selectAverageMetricsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户音频指标平均值\n\n @param userId 用户ID\n @return 用户音频指标平均值\n"}], "constructors": []}