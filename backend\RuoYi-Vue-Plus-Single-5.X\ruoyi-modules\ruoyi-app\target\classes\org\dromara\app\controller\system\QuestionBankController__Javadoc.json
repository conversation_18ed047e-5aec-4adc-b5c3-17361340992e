{"doc": " 题库管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题库列表\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题库列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题库详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 新增题库\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 修改题库\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题库\n"}, {"name": "updateStatus", "paramTypes": ["java.util.Map"], "doc": " 更新题库状态\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.Map"], "doc": " 批量更新题库状态\n"}, {"name": "copyBank", "paramTypes": ["java.util.Map"], "doc": " 复制题库\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n"}, {"name": "getBankStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n"}, {"name": "batchSetSort", "paramTypes": ["java.util.Map"], "doc": " 批量设置题库排序\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": " 导入题库数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}, {"name": "getOptions", "paramTypes": [], "doc": " 获取题库下拉选项\n"}, {"name": "listByMajor", "paramTypes": ["java.lang.Long"], "doc": " 根据专业ID获取题库列表\n"}, {"name": "checkBankCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 题库编码重复校验\n"}, {"name": "getCategoryStats", "paramTypes": [], "doc": " 获取题库分类统计\n"}, {"name": "getDifficultyStats", "paramTypes": [], "doc": " 获取题库难度分布统计\n"}], "constructors": []}