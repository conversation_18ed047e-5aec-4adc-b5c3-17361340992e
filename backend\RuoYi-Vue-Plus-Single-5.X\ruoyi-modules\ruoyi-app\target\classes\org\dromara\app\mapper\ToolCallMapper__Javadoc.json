{"doc": " 工具调用Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectCallHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Long"], "doc": " 分页查询工具调用历史\n\n @param page      分页对象\n @param sessionId 会话ID（可选）\n @param userId    用户ID\n @return 调用历史分页结果\n"}, {"name": "getUserToolStats", "paramTypes": ["java.lang.Long"], "doc": " 查询用户的工具调用统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "getToolUsageStats", "paramTypes": ["java.lang.String"], "doc": " 查询工具的使用统计\n\n @param toolId 工具ID\n @return 统计信息\n"}, {"name": "getPopularTools", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门工具（按调用次数排序）\n\n @param limit 返回数量限制\n @return 热门工具列表\n"}, {"name": "getRecentCalls", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近的工具调用\n\n @param userId 用户ID\n @param limit  返回数量限制\n @return 最近调用列表\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询会话中的工具调用\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 工具调用列表\n"}, {"name": "getErrorStats", "paramTypes": [], "doc": " 统计工具调用错误类型\n\n @return 错误统计\n"}, {"name": "getSlowCalls", "paramTypes": ["java.lang.Long"], "doc": " 查询执行时间超过阈值的工具调用\n\n @param threshold 时间阈值（毫秒）\n @return 慢调用列表\n"}, {"name": "batchInsertToolCalls", "paramTypes": ["java.util.List"], "doc": " 批量插入工具调用记录\n\n @param toolCalls 调用记录列表\n @return 影响行数\n"}, {"name": "selectUserHistory", "paramTypes": ["java.lang.Long", "int", "java.lang.Integer"], "doc": " 查询指定用户的历史调用记录\n\n @param userId   用户ID\n @param offset   偏移量\n @param pageSize 每页大小\n @return 工具调用记录列表\n"}, {"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": " 根据会话ID查询工具调用记录\n\n @param sessionId 会话ID\n @return 工具调用记录列表\n"}], "constructors": []}