{"doc": " 题目详情VO\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 题目ID\n"}, {"name": "title", "doc": " 题目标题\n"}, {"name": "description", "doc": " 题目描述\n"}, {"name": "content", "doc": " 题目内容（Markdown格式）\n"}, {"name": "answer", "doc": " 参考答案（Markdown格式）\n"}, {"name": "analysis", "doc": " 答案解析（Markdown格式）\n"}, {"name": "difficulty", "doc": " 难度等级（简单/中等/困难）\n"}, {"name": "tags", "doc": " 标签列表\n"}, {"name": "acceptanceRate", "doc": " 通过率（百分比）\n"}, {"name": "isCompleted", "doc": " 是否已完成\n"}, {"name": "practiceCount", "doc": " 练习次数\n"}, {"name": "correctRate", "doc": " 正确率（百分比）\n"}, {"name": "commentCount", "doc": " 评论数\n"}, {"name": "isBookmarked", "doc": " 是否收藏\n"}, {"name": "questionType", "doc": " 题目类型（single-单选，multiple-多选，judge-判断，essay-简答，code-编程）\n"}, {"name": "options", "doc": " 题目选项（JSON格式，用于选择题）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " 正确答案\n"}, {"name": "category", "doc": " 分类\n"}, {"name": "bankId", "doc": " 题库ID\n"}, {"name": "bankTitle", "doc": " 题库标题\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}