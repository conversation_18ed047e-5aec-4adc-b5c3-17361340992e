{"doc": " Ollama属性配置\n", "fields": [{"name": "baseUrl", "doc": " Ollama服务器地址\n"}, {"name": "defaultModel", "doc": " 默认模型名称\n"}, {"name": "connectTimeout", "doc": " 连接超时时间（秒）\n"}, {"name": "readTimeout", "doc": " 读取超时时间（秒）\n"}, {"name": "writeTimeout", "doc": " 写入超时时间（秒）\n"}, {"name": "defaultTemperature", "doc": " 默认温度参数\n"}, {"name": "defaultMaxTokens", "doc": " 默认最大Token数\n"}, {"name": "enableStreaming", "doc": " 是否启用流式响应\n"}, {"name": "retryCount", "doc": " 重试次数\n"}, {"name": "retryInterval", "doc": " 重试间隔（毫秒）\n"}, {"name": "enableHealthCheck", "doc": " 是否启用健康检查\n"}, {"name": "healthCheckInterval", "doc": " 健康检查间隔（秒）\n"}, {"name": "embeddingModel", "doc": " 嵌入模型名称\n"}, {"name": "vectorDimension", "doc": " 向量维度\n"}, {"name": "similarityT<PERSON><PERSON>old", "doc": " 相似度阈值\n"}, {"name": "systemPrompts", "doc": " 系统提示词模板\n"}], "enumConstants": [], "methods": [], "constructors": []}