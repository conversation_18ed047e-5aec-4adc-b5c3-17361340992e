{"doc": " 结果分享记录Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询分享记录列表\n\n @param resultId 结果ID\n @return 分享记录列表\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID查询分享记录列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 分享记录列表\n"}, {"name": "selectByShareUrl", "paramTypes": ["java.lang.String"], "doc": " 根据分享链接查询分享记录\n\n @param shareUrl 分享链接\n @return 分享记录\n"}, {"name": "selectByPlatform", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据平台查询分享记录\n\n @param platform 平台\n @param limit    限制数量\n @return 分享记录列表\n"}, {"name": "updateViewCount", "paramTypes": ["java.lang.Long"], "doc": " 更新查看次数\n\n @param id 分享记录ID\n @return 更新数量\n"}, {"name": "selectByStatus", "paramTypes": ["java.lang.String"], "doc": " 根据状态查询分享记录\n\n @param status 状态\n @return 分享记录列表\n"}, {"name": "selectExpiredShares", "paramTypes": [], "doc": " 查询过期的分享记录\n\n @return 分享记录列表\n"}, {"name": "batchUpdateExpiredStatus", "paramTypes": ["java.util.List"], "doc": " 批量更新过期状态\n\n @param ids 分享记录ID列表\n @return 更新数量\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除分享记录\n\n @param resultId 结果ID\n @return 删除数量\n"}], "constructors": []}