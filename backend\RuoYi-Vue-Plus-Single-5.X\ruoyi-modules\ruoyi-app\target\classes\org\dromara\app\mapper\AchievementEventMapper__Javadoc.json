{"doc": " 成就事件数据层\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUnhandledEvents", "paramTypes": ["int"], "doc": " 获取未处理的事件\n\n @param limit 最大数量\n @return 未处理的事件列表\n"}, {"name": "selectUserEvents", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 查询用户特定类型的事件\n\n @param userId    用户ID\n @param eventType 事件类型\n @param limit     最大数量\n @return 事件列表\n"}, {"name": "countUserEvents", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 统计用户特定类型的事件数量\n\n @param userId    用户ID\n @param eventType 事件类型\n @return 事件数量\n"}, {"name": "sumUserEventValues", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 统计用户特定类型的事件值总和\n\n @param userId    用户ID\n @param eventType 事件类型\n @return 事件值总和\n"}, {"name": "selectRecentEvents", "paramTypes": ["java.lang.String", "int"], "doc": " 获取用户最近的事件\n\n @param userId 用户ID\n @param limit  最大数量\n @return 最近的事件列表\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "int"], "doc": " 批量更新事件处理状态\n\n @param eventIds 事件ID列表\n @param status   处理状态\n @return 更新行数\n"}, {"name": "deleteExpiredEvents", "paramTypes": ["int"], "doc": " 删除过期事件\n\n @param days 保留天数\n @return 删除行数\n"}], "constructors": []}