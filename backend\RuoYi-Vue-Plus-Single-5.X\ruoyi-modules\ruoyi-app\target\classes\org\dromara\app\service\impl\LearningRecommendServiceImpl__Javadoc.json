{"doc": " 学习推荐服务实现类\n 基于用户能力评估和学习历史，提供个性化的学习资源推荐\n\n <AUTHOR>\n @date 2025-07-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertVideoToRecommendationItem", "paramTypes": ["org.dromara.app.domain.Video", "java.lang.Long", "java.util.Map"], "doc": " 将视频转换为推荐项VO\n"}, {"name": "convertQuestionBankToRecommendationItem", "paramTypes": ["org.dromara.app.domain.QuestionBank", "java.lang.Long", "java.util.Map"], "doc": " 将题库转换为推荐项VO\n"}, {"name": "convertBookToRecommendationItem", "paramTypes": ["org.dromara.app.domain.Book", "java.lang.Long", "java.util.Map"], "doc": " 将书籍转换为推荐项VO\n"}, {"name": "getPriorityLevel", "paramTypes": ["java.lang.Double"], "doc": " 根据优先级分数获取优先级等级\n"}, {"name": "setTargetCapabilityAndReason", "paramTypes": ["org.dromara.app.domain.vo.RecommendationResponseVo.RecommendationItemVo", "java.util.Map"], "doc": " 设置目标能力和推荐原因\n"}, {"name": "calculateEstimatedTime", "paramTypes": ["java.lang.Integer"], "doc": " 计算题库预估时间\n"}, {"name": "calculateBookEstimatedTime", "paramTypes": ["java.lang.Integer"], "doc": " 计算书籍预估阅读时间\n"}, {"name": "convertToLocalDateTime", "paramTypes": ["java.util.Date"], "doc": " 转换Date到LocalDateTime\n"}, {"name": "convertDifficultyLevel", "paramTypes": ["java.lang.Integer"], "doc": " 转换难度等级（数字转文字）\n"}], "constructors": []}