{"doc": " 用户行为记录Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processUserBehavior", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto"], "doc": " 处理用户行为数据\n\n @param trackEventDto 埋点事件数据\n"}, {"name": "batchProcessUserBehavior", "paramTypes": ["java.util.List"], "doc": " 批量处理用户行为数据\n\n @param trackEventDtos 埋点事件数据列表\n"}, {"name": "insertUserBehavior", "paramTypes": ["org.dromara.app.domain.bo.UserBehaviorBo"], "doc": " 保存用户行为记录\n\n @param userBehaviorBo 用户行为业务对象\n @return 保存结果\n"}, {"name": "batchInsertUserBehavior", "paramTypes": ["java.util.List"], "doc": " 批量保存用户行为记录\n\n @param userBehaviorBos 用户行为业务对象列表\n @return 保存结果\n"}, {"name": "queryByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和行为类型查询行为记录\n\n @param userId       用户ID\n @param behaviorType 行为类型\n @return 行为记录列表\n"}, {"name": "countByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 统计用户某种行为的次数\n\n @param userId       用户ID\n @param behaviorType 行为类型\n @return 行为次数\n"}, {"name": "getConsecutiveLoginDays", "paramTypes": ["java.lang.Long"], "doc": " 查询用户连续登录天数\n\n @param userId 用户ID\n @return 连续登录天数\n"}, {"name": "getTotalStudyMinutes", "paramTypes": ["java.lang.Long"], "doc": " 查询用户累计学习时长（分钟）\n\n @param userId 用户ID\n @return 累计学习时长\n"}], "constructors": []}