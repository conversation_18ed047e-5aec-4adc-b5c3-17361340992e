{"doc": " 视频播放记录缓存服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildRecordKey", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 构建播放记录缓存键\n"}, {"name": "buildUserHistoryKey", "paramTypes": ["java.lang.Long"], "doc": " 构建用户历史记录缓存键\n"}, {"name": "cachePlayRecord", "paramTypes": ["org.dromara.app.domain.VideoPlayRecord"], "doc": " 缓存播放记录\n"}, {"name": "getCachedPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取播放记录缓存\n"}, {"name": "updatePlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 更新播放记录缓存\n"}, {"name": "getUserVideoHistory", "paramTypes": ["java.lang.Long", "int", "int"], "doc": " 获取用户观看历史视频ID列表（按时间倒序）\n"}, {"name": "deletePlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除播放记录缓存\n"}, {"name": "preloadUserHistory", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 批量预热用户历史记录到缓存\n"}, {"name": "isRecordCached", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 检查播放记录是否存在于缓存中\n"}], "constructors": []}