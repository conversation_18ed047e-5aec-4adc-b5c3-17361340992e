{"doc": " 文本处理工具执行器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processText", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 处理文本\n"}, {"name": "countText", "paramTypes": ["java.lang.String"], "doc": " 统计文本\n"}, {"name": "formatText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 格式化文本\n"}, {"name": "extractText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 提取文本\n"}, {"name": "replaceText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 替换文本\n"}, {"name": "analyzeText", "paramTypes": ["java.lang.String"], "doc": " 分析文本\n"}, {"name": "capitalizeWords", "paramTypes": ["java.lang.String"], "doc": " 首字母大写\n"}, {"name": "extractEmails", "paramTypes": ["java.lang.String"], "doc": " 提取邮箱\n"}, {"name": "extractUrls", "paramTypes": ["java.lang.String"], "doc": " 提取URL\n"}, {"name": "extractPhones", "paramTypes": ["java.lang.String"], "doc": " 提取电话号码\n"}, {"name": "extractNumbers", "paramTypes": ["java.lang.String"], "doc": " 提取数字\n"}, {"name": "extractKeywords", "paramTypes": ["java.lang.String"], "doc": " 提取关键词（简单实现）\n"}, {"name": "detectLanguage", "paramTypes": ["java.lang.String"], "doc": " 检测语言（简单实现）\n"}, {"name": "analyzeSentiment", "paramTypes": ["java.lang.String"], "doc": " 情感分析（简单实现）\n"}], "constructors": []}