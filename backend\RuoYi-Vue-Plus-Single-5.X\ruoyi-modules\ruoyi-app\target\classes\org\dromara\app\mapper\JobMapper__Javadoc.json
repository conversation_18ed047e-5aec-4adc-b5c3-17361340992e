{"doc": " 岗位信息Mapper接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectJobPageWithFavorite", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.app.domain.bo.JobQueryBo"], "doc": " 分页查询岗位列表（包含收藏状态）\n\n @param page    分页参数\n @param queryBo 查询条件\n @return 岗位列表\n"}, {"name": "selectRecommendedJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 查询推荐岗位\n\n @param limit  限制数量\n @param userId 用户ID\n @return 推荐岗位列表\n"}, {"name": "selectHotJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 查询热门岗位\n\n @param limit  限制数量\n @param userId 用户ID\n @return 热门岗位列表\n"}, {"name": "incrementViewCount", "paramTypes": ["java.lang.Long"], "doc": " 增加浏览次数\n\n @param jobId 岗位ID\n @return 影响行数\n"}, {"name": "updateFavoriteCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新收藏次数\n\n @param jobId     岗位ID\n @param increment 增量（1或-1）\n @return 影响行数\n"}, {"name": "selectJobStatistics", "paramTypes": [], "doc": " 查询岗位统计信息\n\n @return 统计信息\n"}], "constructors": []}