{"doc": " AI工具服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "executeSingleBatchCall", "paramTypes": ["org.dromara.app.service.IToolService.BatchToolCall", "java.lang.Long"], "doc": " 执行单个批量调用\n"}, {"name": "parseJsonArrayFormat", "paramTypes": ["java.lang.String"], "doc": " 解析JSON数组格式的工具调用\n 格式: [{\"tool\": \"calculator\", \"parameters\": {\"expression\": \"2+3\"}, \"reasoning\": \"计算表达式\"}]\n"}, {"name": "parseXmlFormat", "paramTypes": ["java.lang.String"], "doc": " 解析XML格式的工具调用\n 格式: <tool_call><tool>calculator</tool><parameters>{\"expression\": \"2+3\"}</parameters></tool_call>\n"}, {"name": "parseMarkdownFormat", "paramTypes": ["java.lang.String"], "doc": " 解析Markdown格式的工具调用\n 格式: ```tool:calculator\\n{\"expression\": \"2+3\"}\\n```\n"}, {"name": "parseFunctionCallFormat", "paramTypes": ["java.lang.String"], "doc": " 解析函数调用格式\n 格式: calculator(expression=\"2+3\")\n"}, {"name": "validateParametersAgainstSchema", "paramTypes": ["org.dromara.app.domain.AiTool.ParameterSchema", "java.util.Map", "java.util.List"], "doc": " 根据Schema验证参数\n"}, {"name": "validateParameterProperty", "paramTypes": ["java.lang.String", "java.lang.Object", "org.dromara.app.domain.AiTool.ParameterSchema.ParameterProperty", "java.util.List"], "doc": " 验证单个参数属性\n"}, {"name": "enhanceUserStats", "paramTypes": ["java.util.Map", "java.lang.Long"], "doc": " 增强用户统计信息\n"}, {"name": "enhanceToolStats", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": " 增强工具统计信息\n"}, {"name": "validateToolConfiguration", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 验证工具配置完整性\n"}, {"name": "checkSpecificPermission", "paramTypes": ["org.dromara.app.domain.AiTool", "java.lang.Long"], "doc": " 检查特定权限\n"}, {"name": "clearToolCache", "paramTypes": ["java.lang.String"], "doc": " 清除工具相关缓存\n"}, {"name": "createFailureResult", "paramTypes": ["org.dromara.app.domain.ToolCall", "long", "java.lang.String"], "doc": " 创建失败结果并更新调用记录\n"}, {"name": "executeWithConfig", "paramTypes": ["org.dromara.app.tool.ToolExecutor", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "org.dromara.app.domain.AiTool"], "doc": " 使用工具配置执行工具\n"}, {"name": "executeWithTimeout", "paramTypes": ["org.dromara.app.tool.ToolExecutor", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "int"], "doc": " 带超时的工具执行\n"}, {"name": "getAsyncExecutor", "paramTypes": [], "doc": " 获取异步执行器\n"}, {"name": "validateAndDeduplicateParsedCalls", "paramTypes": ["java.util.List"], "doc": " 验证和去重解析的工具调用\n"}, {"name": "isValidToolName", "paramTypes": ["java.lang.String"], "doc": " 验证工具名是否有效\n"}, {"name": "parseFunctionParameters", "paramTypes": ["java.lang.String"], "doc": " 解析函数参数\n"}, {"name": "validateNormalizedParameters", "paramTypes": ["org.dromara.app.domain.AiTool.ParameterSchema", "java.util.Map", "java.util.List"], "doc": " 验证标准化后的参数\n"}, {"name": "isValidParameterType", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": " 验证参数类型\n"}, {"name": "isInteger", "paramTypes": ["java.lang.String"], "doc": " 检查字符串是否为整数\n"}, {"name": "isNumber", "paramTypes": ["java.lang.String"], "doc": " 检查字符串是否为数字\n"}, {"name": "isBooleanString", "paramTypes": ["java.lang.String"], "doc": " 检查字符串是否为布尔值\n"}, {"name": "processToolCallData", "paramTypes": ["org.dromara.app.domain.ToolCall"], "doc": " 处理工具调用记录数据\n"}, {"name": "updateAsyncCallFailure", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 更新异步调用失败状态\n"}, {"name": "updateAsyncCallResult", "paramTypes": ["java.lang.String", "java.lang.Long", "org.dromara.app.service.IToolService.ToolCallResult", "java.lang.String"], "doc": " 更新异步调用结果\n"}], "constructors": []}