{"doc": " Prompt工程服务\n 负责优化和管理AI提示词\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildRagEnhancedPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String", "int"], "doc": " 构建RAG增强提示词\n\n @param userQuery        用户查询\n @param retrievalResults 检索结果\n @param agentType        Agent类型\n @param contextWindow    上下文窗口大小\n @return 增强后的提示词\n"}, {"name": "buildToolCallPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.util.List"], "doc": " 构建工具调用提示词\n\n @param userQuery           用户查询\n @param availableTools      可用工具列表\n @param conversationHistory 对话历史\n @return 工具调用提示词\n"}, {"name": "optimizeSystemPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 优化系统提示词\n\n @param originalPrompt 原始提示词\n @param agentType      Agent类型\n @param userContext    用户上下文\n @return 优化后的提示词\n"}, {"name": "buildConversationalPrompt", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": " 构建多轮对话提示词\n\n @param currentQuery        当前查询\n @param conversationHistory 对话历史\n @param maxHistoryLength    最大历史长度\n @return 多轮对话提示词\n"}, {"name": "buildChainOfThoughtPrompt", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建思维链提示词\n\n @param userQuery 用户查询\n @param taskType  任务类型\n @return 思维链提示词\n"}, {"name": "buildFewShotPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String"], "doc": " 构建少样本学习提示词\n\n @param userQuery       用户查询\n @param examples        示例列表\n @param taskDescription 任务描述\n @return 少样本学习提示词\n"}, {"name": "adjust<PERSON>rompt<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "int", "org.dromara.app.service.PromptEngineeringService.LengthAdjustmentStrategy"], "doc": " 动态调整提示词长度\n\n @param prompt    原始提示词\n @param maxTokens 最大token数\n @param priority  优先级策略\n @return 调整后的提示词\n"}, {"name": "extractKeyInformation", "paramTypes": ["java.lang.String", "int"], "doc": " 提取关键信息\n\n @param content   内容\n @param maxLength 最大长度\n @return 关键信息\n"}], "constructors": []}