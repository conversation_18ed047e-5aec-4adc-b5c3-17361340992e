{"doc": " 问题分析对象 app_question_analysis\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [{"name": "id", "doc": " 分析ID\n"}, {"name": "resultId", "doc": " 结果ID\n"}, {"name": "questionId", "doc": " 问题ID\n"}, {"name": "question", "doc": " 问题内容\n"}, {"name": "category", "doc": " 问题分类\n"}, {"name": "difficulty", "doc": " 难度等级（1-5）\n"}, {"name": "answer", "doc": " 用户答案\n"}, {"name": "score", "doc": " 得分\n"}, {"name": "audioScore", "doc": " 音频得分\n"}, {"name": "videoScore", "doc": " 视频得分\n"}, {"name": "textScore", "doc": " 文本得分\n"}, {"name": "feedback", "doc": " 反馈\n"}, {"name": "strengths", "doc": " 优势点（JSON数组）\n"}, {"name": "weaknesses", "doc": " 劣势点（JSON数组）\n"}, {"name": "keywordMatches", "doc": " 关键词匹配（JSON数组）\n"}, {"name": "idealAnswer", "doc": " 理想答案\n"}, {"name": "timeSpent", "doc": " 用时（秒）\n"}, {"name": "timeLimit", "doc": " 时间限制（秒）\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}