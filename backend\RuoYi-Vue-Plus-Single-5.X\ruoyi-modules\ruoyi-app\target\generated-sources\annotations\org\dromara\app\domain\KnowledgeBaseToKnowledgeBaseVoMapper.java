package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__80;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__80.class,
    uses = {KnowledgeBaseBoToKnowledgeBaseMapper.class,KnowledgeBaseVoToKnowledgeBaseMapper.class},
    imports = {}
)
public interface KnowledgeBaseToKnowledgeBaseVoMapper extends BaseMapper<KnowledgeBase, KnowledgeBaseVo> {
}
