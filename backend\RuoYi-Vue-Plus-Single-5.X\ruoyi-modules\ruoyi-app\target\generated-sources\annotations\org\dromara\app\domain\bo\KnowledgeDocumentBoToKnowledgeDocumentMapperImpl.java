package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeDocument;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:32:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeDocumentBoToKnowledgeDocumentMapperImpl implements KnowledgeDocumentBoToKnowledgeDocumentMapper {

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocument knowledgeDocument = new KnowledgeDocument();

        knowledgeDocument.setId( arg0.getId() );
        knowledgeDocument.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocument.setTitle( arg0.getTitle() );
        knowledgeDocument.setContent( arg0.getContent() );
        knowledgeDocument.setDocType( arg0.getDocType() );
        knowledgeDocument.setSource( arg0.getSource() );
        knowledgeDocument.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocument.setFilePath( arg0.getFilePath() );
        knowledgeDocument.setFileSize( arg0.getFileSize() );
        knowledgeDocument.setStatus( arg0.getStatus() );
        knowledgeDocument.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocument.setSummary( arg0.getSummary() );
        knowledgeDocument.setTags( arg0.getTags() );
        knowledgeDocument.setMetadata( arg0.getMetadata() );
        knowledgeDocument.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocument.setSortOrder( arg0.getSortOrder() );
        knowledgeDocument.setRemark( arg0.getRemark() );

        return knowledgeDocument;
    }

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentBo arg0, KnowledgeDocument arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setDocType( arg0.getDocType() );
        arg1.setSource( arg0.getSource() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
