{"doc": " 报告生成服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateInterviewReport", "paramTypes": ["java.lang.String"], "doc": " 生成面试报告\n\n @param sessionId 面试会话ID\n @return 报告数据\n"}, {"name": "generatePdfReport", "paramTypes": ["java.lang.String"], "doc": " 生成PDF报告\n\n @param sessionId 面试会话ID\n @return PDF文件路径\n"}, {"name": "generateRadarChartData", "paramTypes": ["java.util.List"], "doc": " 生成雷达图数据\n\n @param dimensionScores 维度评分列表\n @return 雷达图数据\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 生成改进建议\n\n @param weaknesses 弱点列表\n @param dimensionScores 维度评分列表\n @return 改进建议列表\n"}, {"name": "generateLearningPathRecommendations", "paramTypes": ["java.lang.String", "org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": " 生成学习路径推荐\n\n @param sessionId 会话ID\n @param reportData 报告数据\n @return 学习路径推荐\n"}], "constructors": []}