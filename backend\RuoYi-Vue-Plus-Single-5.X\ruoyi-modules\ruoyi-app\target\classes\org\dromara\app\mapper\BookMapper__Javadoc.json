{"doc": " 面试书籍Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBookPageWithUserInfo", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 分页查询书籍列表（支持分类筛选和搜索）\n\n @param page        分页参数\n @param category    分类筛选\n @param searchQuery 搜索关键词\n @param userId      用户ID（用于查询购买状态和阅读进度）\n @return 书籍列表\n"}, {"name": "selectBookByIdWithUserInfo", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据ID查询书籍详情（包含用户阅读信息）\n\n @param id     书籍ID\n @param userId 用户ID\n @return 书籍详情\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": " 增加书籍阅读次数\n\n @param bookId 书籍ID\n @return 影响行数\n"}, {"name": "selectHotBooks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门书籍列表\n\n @param limit 限制数量\n @return 热门书籍列表\n"}, {"name": "selectRecommendedBooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询推荐书籍列表（基于用户阅读历史）\n\n @param userId 用户ID\n @param limit  限制数量\n @return 推荐书籍列表\n"}, {"name": "selectCategoryStats", "paramTypes": [], "doc": " 根据分类查询书籍数量统计\n\n @return 分类统计信息\n"}], "constructors": []}