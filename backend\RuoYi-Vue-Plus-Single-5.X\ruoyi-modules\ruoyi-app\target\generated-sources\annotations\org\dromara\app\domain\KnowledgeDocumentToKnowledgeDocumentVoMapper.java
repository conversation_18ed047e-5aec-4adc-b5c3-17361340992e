package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__80;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__80.class,
    uses = {KnowledgeDocumentVoToKnowledgeDocumentMapper.class,KnowledgeDocumentBoToKnowledgeDocumentMapper.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}
