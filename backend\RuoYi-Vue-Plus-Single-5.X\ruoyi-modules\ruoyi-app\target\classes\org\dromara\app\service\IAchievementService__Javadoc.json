{"doc": " 成就系统服务接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成就列表\n\n @param pageQuery 分页查询条件\n @return 成就列表\n"}, {"name": "queryActiveAchievements", "paramTypes": [], "doc": " 查询激活的成就列表\n\n @return 激活的成就列表\n"}, {"name": "queryByAchievementType", "paramTypes": ["java.lang.String"], "doc": " 根据成就类型查询成就列表\n\n @param achievementType 成就类型\n @return 成就列表\n"}, {"name": "queryByAchievementCode", "paramTypes": ["java.lang.String"], "doc": " 根据成就代码查询成就\n\n @param achievementCode 成就代码\n @return 成就信息\n"}, {"name": "checkAchievements", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 检查用户成就\n\n @param userId        用户ID\n @param trackEventDto 用户行为事件\n"}, {"name": "getUserBadges", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 获取用户徽章列表\n\n @param userId   用户ID\n @param category 徽章类别（可选）\n @param unlocked 是否解锁（可选）\n @param rarity   稀有度（可选）\n @return 徽章列表\n"}, {"name": "getBadgeDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取徽章详情\n\n @param userId  用户ID\n @param badgeId 徽章ID\n @return 徽章详情\n"}, {"name": "setPinStatus", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 设置徽章置顶状态\n\n @param userId   用户ID\n @param badgeId  徽章ID\n @param isPinned 是否置顶\n"}, {"name": "getPinnedBadges", "paramTypes": ["java.lang.String"], "doc": " 获取置顶徽章列表\n\n @param userId 用户ID\n @return 置顶徽章列表\n"}, {"name": "getAchievementStats", "paramTypes": ["java.lang.String"], "doc": " 获取成就统计信息\n\n @param userId 用户ID\n @return 成就统计信息\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取最近解锁的成就\n\n @param userId 用户ID\n @param limit  数量限制\n @return 最近解锁的成就\n"}, {"name": "getCategories", "paramTypes": [], "doc": " 获取成就分类列表\n\n @return 成就分类列表（键为类别代码，值为类别名称）\n"}, {"name": "getInProgressAchievements", "paramTypes": ["java.lang.String"], "doc": " 获取进行中的成就\n\n @param userId 用户ID\n @return 进行中的成就\n"}, {"name": "getUserAchievementDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户成就详情\n\n @param userId        用户ID\n @param achievementId 成就ID\n @return 用户成就详情\n"}, {"name": "checkAndUpdateAchievements", "paramTypes": ["java.lang.String"], "doc": " 检查并更新用户成就进度\n\n @param userId 用户ID\n @return 新解锁的成就列表\n"}, {"name": "shareAchievements", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 分享成就墙\n\n @param userId   用户ID\n @param platform 平台（wechat, qq, weibo, link）\n @return 分享结果\n"}, {"name": "recordEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 记录用户行为事件\n\n @param userId      用户ID\n @param eventType   事件类型\n @param eventData   事件数据\n @param eventValue  事件值\n @param relatedId   关联对象ID\n @param relatedType 关联对象类型\n @return 是否触发了新成就\n"}, {"name": "unlockAchievement", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 解锁特定成就\n\n @param userId        用户ID\n @param achievementId 成就ID\n @param source        解锁来源\n @return 是否解锁成功\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取推荐接下来完成的成就\n\n @param userId 用户ID\n @param limit  数量限制\n @return 推荐成就列表\n"}, {"name": "getLeaderboard", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取成就排行榜\n\n @param category 类别（可选）\n @param limit    数量限制\n @return 排行榜列表\n"}, {"name": "getUserRankingInfo", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户在排行榜中的位置\n\n @param userId   用户ID\n @param category 类别（可选）\n @return 排行榜信息\n"}, {"name": "processUnhandledEvents", "paramTypes": ["int"], "doc": " 处理未处理的事件\n\n @param maxEvents 最大处理事件数\n @return 处理的事件数量\n"}, {"name": "updateAchievementProgress", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.util.Map"], "doc": " 更新用户成就进度\n\n @param userId        用户ID\n @param achievementId 成就ID\n @param progress      进度值\n @param eventData     事件数据\n @return 是否触发了成就解锁\n"}, {"name": "initializeUserAchievements", "paramTypes": ["java.lang.String"], "doc": " 批量初始化用户成就\n\n @param userId 用户ID\n @return 初始化的成就数量\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 获取成就事件统计\n\n @param userId    用户ID\n @param eventType 事件类型（可选）\n @param days      统计天数\n @return 事件统计信息\n"}, {"name": "cleanupExpiredEvents", "paramTypes": ["int"], "doc": " 清理过期事件\n\n @param days 保留天数\n @return 清理的事件数量\n"}, {"name": "getUserAchievementCompletion", "paramTypes": ["java.lang.String"], "doc": " 获取用户成就完成度\n\n @param userId 用户ID\n @return 完成度信息\n"}, {"name": "recalculateUserProgress", "paramTypes": ["java.lang.String"], "doc": " 重新计算用户成就进度\n\n @param userId 用户ID\n @return 重新计算的成就数量\n"}, {"name": "getRecentUserAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取最近解锁的成就\n\n @param userId 用户ID\n @param limit  数量限制\n @return 最近解锁的成就列表\n"}], "constructors": []}