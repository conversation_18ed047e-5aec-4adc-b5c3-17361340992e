{"doc": " 讯飞数字人认证服务\n\n <AUTHOR>\n @date 2025-07-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "assembleRequestUrl", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 组装WebSocket请求URL（带认证信息）\n\n @param requestUrl WebSocket URL\n @param apiKey     API Key\n @param apiSecret  API Secret\n @return 带认证信息的URL\n"}, {"name": "assembleRequestUrl", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 组装WebSocket请求URL（带认证信息）\n\n @param requestUrl WebSocket URL\n @param apiKey     API Key\n @param apiSecret  API Secret\n @param method     HTTP方法\n @return 带认证信息的URL\n"}, {"name": "assembleRequestHeader", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "byte[]"], "doc": " 计算签名所需要的header参数（HTTP接口）\n\n @param requestUrl HTTP请求URL\n @param apiKey     API Key\n @param apiSecret  API Secret\n @param method     HTTP方法\n @param body       HTTP请求体\n @return header map，包含所有需要设置的请求头\n"}], "constructors": []}