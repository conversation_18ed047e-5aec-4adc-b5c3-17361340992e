{"doc": " 多模态分析服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "comprehensiveAnalysis", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile", "org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": " 综合多模态分析\n\n @param sessionId 面试会话ID\n @param audioFile 音频文件\n @param videoFile 视频文件\n @param textContent 文本内容\n @param jobPosition 岗位信息\n @return 综合分析结果\n"}, {"name": "analyzeAudio", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 音频分析\n\n @param audioFile 音频文件\n @param sessionId 会话ID\n @return 音频分析结果\n"}, {"name": "analyzeVideo", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 视频分析\n\n @param videoFile 视频文件\n @param sessionId 会话ID\n @return 视频分析结果\n"}, {"name": "analyzeText", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 文本分析\n\n @param textContent 文本内容\n @param jobPosition 岗位信息\n @param sessionId 会话ID\n @return 文本分析结果\n"}, {"name": "analyzeAudioStream", "paramTypes": ["byte[]", "java.lang.String", "org.dromara.app.service.IMultimodalAnalysisService.AnalysisCallback"], "doc": " 实时音频分析（流式）\n\n @param audioStream 音频流数据\n @param sessionId 会话ID\n @param callback 实时回调\n"}, {"name": "analyzeVideoFrame", "paramTypes": ["byte[]", "java.lang.String", "org.dromara.app.service.IMultimodalAnalysisService.AnalysisCallback"], "doc": " 实时视频分析（流式）\n\n @param videoFrame 视频帧数据\n @param sessionId 会话ID\n @param callback 实时回调\n"}, {"name": "getAnalysisHistory", "paramTypes": ["java.lang.String"], "doc": " 获取分析历史\n\n @param sessionId 会话ID\n @return 分析历史记录\n"}], "constructors": []}