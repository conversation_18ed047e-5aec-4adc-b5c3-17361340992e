{"doc": " AI工具服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取可用工具列表\n\n @param category 工具分类（可选）\n @param userId   用户ID\n @return 工具列表\n"}, {"name": "getToolDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取工具详情\n\n @param toolId 工具ID\n @param userId 用户ID\n @return 工具详情\n"}, {"name": "checkToolPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查工具权限\n\n @param toolId 工具ID\n @param userId 用户ID\n @return 是否有权限\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 注册工具\n\n @param tool 工具信息\n @return 是否成功\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 更新工具\n\n @param tool 工具信息\n @return 是否成功\n"}, {"name": "deleteTool", "paramTypes": ["java.lang.String"], "doc": " 删除工具\n\n @param toolId 工具ID\n @return 是否成功\n"}, {"name": "toggleTool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 启用/禁用工具\n\n @param toolId  工具ID\n @param enabled 是否启用\n @return 是否成功\n"}, {"name": "executeTool", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "java.lang.Long"], "doc": " 执行工具调用\n\n @param toolId     工具ID\n @param parameters 调用参数\n @param context    调用上下文\n @param userId     用户ID\n @return 调用结果\n"}, {"name": "executeToolAsync", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "java.lang.Long"], "doc": " 异步执行工具调用\n\n @param toolId     工具ID\n @param parameters 调用参数\n @param context    调用上下文\n @param userId     用户ID\n @return 调用记录ID\n"}, {"name": "executeBatchTools", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量执行工具调用\n\n @param toolCalls 工具调用列表\n @param userId    用户ID\n @return 调用结果列表\n"}, {"name": "parseToolCalls", "paramTypes": ["java.lang.String"], "doc": " 解析AI消息中的工具调用\n\n @param aiMessage AI消息内容\n @return 工具调用列表\n"}, {"name": "validateParameters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 验证工具调用参数\n\n @param toolId     工具ID\n @param parameters 参数\n @return 验证结果\n"}, {"name": "getToolCallHistory", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取工具调用记录\n\n @param userId   用户ID\n @param pageNum  页码\n @param pageSize 每页大小\n @return 调用记录分页结果\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取会话的工具调用记录\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 调用记录列表\n"}, {"name": "getToolCallDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取工具调用详情\n\n @param callId 调用记录ID\n @param userId 用户ID\n @return 调用详情\n"}, {"name": "retryToolCall", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 重新执行工具调用\n\n @param callId 调用记录ID\n @param userId 用户ID\n @return 新的调用结果\n"}, {"name": "getToolUsageStats", "paramTypes": ["java.lang.Long"], "doc": " 获取工具使用统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "getToolPerformanceStats", "paramTypes": ["java.lang.String"], "doc": " 获取工具性能统计\n\n @param toolId 工具ID\n @return 性能统计\n"}, {"name": "initSystemTools", "paramTypes": [], "doc": " 初始化系统工具\n"}], "constructors": []}