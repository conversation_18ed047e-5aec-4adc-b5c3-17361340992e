{"doc": " 活动统计响应VO\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "today", "doc": " 今日活动时长(毫秒)\n"}, {"name": "week", "doc": " 本周活动时长(毫秒)\n"}, {"name": "month", "doc": " 本月活动时长(毫秒)\n"}, {"name": "total", "doc": " 总活动时长(毫秒)\n"}, {"name": "todayFormatted", "doc": " 今日活动时长(格式化)\n"}, {"name": "weekFormatted", "doc": " 本周活动时长(格式化)\n"}, {"name": "monthFormatted", "doc": " 本月活动时长(格式化)\n"}, {"name": "totalFormatted", "doc": " 总活动时长(格式化)\n"}, {"name": "byType", "doc": " 按类型统计的活动时长\n"}], "enumConstants": [], "methods": [{"name": "formatDuration", "paramTypes": ["java.lang.Long"], "doc": " 格式化时长为可读字符串\n\n @param duration 时长(毫秒)\n @return 格式化后的字符串\n"}, {"name": "setFormattedDurations", "paramTypes": [], "doc": " 设置格式化的时长字符串\n"}], "constructors": []}