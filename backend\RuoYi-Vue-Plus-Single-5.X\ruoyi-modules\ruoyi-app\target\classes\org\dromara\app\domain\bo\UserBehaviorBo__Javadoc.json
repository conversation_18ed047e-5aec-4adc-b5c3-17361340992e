{"doc": " 用户行为记录业务对象 app_user_behavior\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 主键ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "behaviorType", "doc": " 行为类型\n"}, {"name": "behaviorData", "doc": " 行为数据(JSON格式)\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": " IP地址\n"}, {"name": "userAgent", "doc": " 用户代理\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}