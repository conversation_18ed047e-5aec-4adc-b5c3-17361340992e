{"doc": " 成就规则引擎\n 负责解析和执行成就的触发条件\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkAchievementCondition", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.Achievement", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 检查成就条件是否满足\n\n @param userId        用户ID\n @param achievement   成就信息\n @param trackEventDto 用户行为事件\n @return 是否满足条件\n"}, {"name": "calculateProgress", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.Achievement", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 计算成就进度\n\n @param userId        用户ID\n @param achievement   成就信息\n @param trackEventDto 用户行为事件\n @return 进度信息 [当前值, 目标值, 进度百分比]\n"}, {"name": "executeRule", "paramTypes": ["java.lang.Long", "java.lang.String", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 执行具体的规则检查\n"}, {"name": "checkLoginRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查登录类成就规则\n"}, {"name": "checkLearningRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查学习类成就规则\n"}, {"name": "checkSocialRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查社交类成就规则\n"}, {"name": "checkTimeRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 检查时间类成就规则\n"}, {"name": "checkCustomRule", "paramTypes": ["java.lang.Long", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": " 检查自定义成就规则\n"}, {"name": "getCurrentValue", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 获取当前数值\n"}, {"name": "getTargetValue", "paramTypes": ["java.util.Map"], "doc": " 获取目标数值\n"}], "constructors": []}