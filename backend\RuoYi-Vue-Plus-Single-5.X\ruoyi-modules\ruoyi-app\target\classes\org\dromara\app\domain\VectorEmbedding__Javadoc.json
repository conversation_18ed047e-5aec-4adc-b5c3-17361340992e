{"doc": " 向量嵌入对象，用于知识库检索\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 唯一标识\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "knowledgeBaseId", "doc": " 知识库ID\n"}, {"name": "documentId", "doc": " 文档ID\n"}, {"name": "content", "doc": " 文档内容\n"}, {"name": "embedding", "doc": " 嵌入向量（PGvector格式）\n"}, {"name": "title", "doc": " 文档标题\n"}, {"name": "summary", "doc": " 摘要\n"}, {"name": "position", "doc": " 位置\n"}, {"name": "contentLength", "doc": " 内容长度\n"}, {"name": "chunkType", "doc": " 分块类型\n"}, {"name": "modelName", "doc": " 模型名称\n"}, {"name": "dimension", "doc": " 向量维度\n"}, {"name": "metadata", "doc": " 元数据\n"}, {"name": "tags", "doc": " 标签\n"}, {"name": "sortOrder", "doc": " 排序\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createDept", "doc": " 创建部门\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateBy", "doc": " 更新者\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "delFlag", "doc": " 删除标志\n"}, {"name": "version", "doc": " 版本号\n"}, {"name": "similarity", "doc": " 相似度得分（查询时使用）\n"}, {"name": "source", "doc": " 来源\n"}, {"name": "documentType", "doc": " 文档类型\n"}], "enumConstants": [], "methods": [], "constructors": []}