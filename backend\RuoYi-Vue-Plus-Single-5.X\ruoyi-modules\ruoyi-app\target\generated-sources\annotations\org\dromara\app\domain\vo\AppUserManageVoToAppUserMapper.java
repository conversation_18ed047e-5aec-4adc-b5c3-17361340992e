package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__80;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.AppUserToAppUserManageVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__80.class,
    uses = {AppUserToAppUserManageVoMapper.class},
    imports = {}
)
public interface AppUserManageVoToAppUserMapper extends BaseMapper<AppUserManageVo, AppUser> {
}
