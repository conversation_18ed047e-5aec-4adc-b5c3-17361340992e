{"doc": " 书籍章节Repository接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "findByBookIdAndStatusTrueOrderByChapterOrderAsc", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID查询章节列表\n\n @param bookId 书籍ID\n @return 章节列表（按顺序排序）\n"}, {"name": "findByBookIdAndChapterOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据书籍ID和章节序号查询章节\n\n @param bookId       书籍ID\n @param chapterOrder 章节序号\n @return 章节信息\n"}, {"name": "findPreviewChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID查询试读章节列表\n\n @param bookId 书籍ID\n @return 试读章节列表\n"}, {"name": "findUnlockedChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID查询已解锁章节列表\n\n @param bookId 书籍ID\n @return 已解锁章节列表\n"}, {"name": "countByBookIdAndStatusTrue", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID统计章节数量\n\n @param bookId 书籍ID\n @return 章节数量\n"}, {"name": "deleteByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID删除所有章节\n\n @param bookId 书籍ID\n"}, {"name": "findMaxChapterOrderByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID查询最大章节序号\n\n @param bookId 书籍ID\n @return 最大章节序号\n"}], "constructors": []}