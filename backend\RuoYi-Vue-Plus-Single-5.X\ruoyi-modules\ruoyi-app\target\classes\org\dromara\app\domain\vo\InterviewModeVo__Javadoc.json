{"doc": " 面试模式视图对象 app_interview_mode\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 模式ID\n"}, {"name": "name", "doc": " 模式名称\n"}, {"name": "description", "doc": " 模式描述\n"}, {"name": "icon", "doc": " 模式图标\n"}, {"name": "color", "doc": " 模式颜色\n"}, {"name": "duration", "doc": " 默认时长（分钟）\n"}, {"name": "difficulty", "doc": " 难度等级（1-5）\n"}, {"name": "features", "doc": " 模式特性\n"}, {"name": "sortOrder", "doc": " 排序号\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}