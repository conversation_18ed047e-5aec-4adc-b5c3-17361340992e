{"doc": " 面试会话对象 app_interview_session\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "jobId", "doc": " 岗位ID\n"}, {"name": "modeId", "doc": " 面试模式ID\n"}, {"name": "sessionToken", "doc": " 会话令牌\n"}, {"name": "estimatedDuration", "doc": " 预计时长（分钟）\n"}, {"name": "questionCount", "doc": " 题目数量\n"}, {"name": "resumeUrl", "doc": " 简历URL\n"}, {"name": "customizedQuestions", "doc": " 自定义问题（JSON数组）\n"}, {"name": "status", "doc": " 状态（created,started,paused,completed,expired）\n"}, {"name": "startTime", "doc": " 开始时间\n"}, {"name": "endTime", "doc": " 结束时间\n"}, {"name": "expiresAt", "doc": " 过期时间\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}], "enumConstants": [], "methods": [], "constructors": []}