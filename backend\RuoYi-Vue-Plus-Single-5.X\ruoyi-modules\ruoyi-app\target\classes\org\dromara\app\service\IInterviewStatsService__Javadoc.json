{"doc": " 面试统计服务接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户面试统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "getJobStats", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位面试统计\n\n @param jobId 岗位ID\n @return 统计信息\n"}, {"name": "getSystemStats", "paramTypes": [], "doc": " 获取系统整体统计\n\n @return 系统统计\n"}, {"name": "getInterviewTrends", "paramTypes": ["java.time.LocalDateTime", "java.time.LocalDateTime", "java.lang.String"], "doc": " 获取面试趋势数据\n\n @param startTime 开始时间\n @param endTime   结束时间\n @param granularity 粒度（day, week, month）\n @return 趋势数据\n"}, {"name": "getPopularQuestions", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门问题统计\n\n @param limit 限制数量\n @return 热门问题列表\n"}, {"name": "getUserRankings", "paramTypes": ["java.lang.Integer"], "doc": " 获取用户表现排行\n\n @param limit 限制数量\n @return 排行榜\n"}, {"name": "getCompletionRates", "paramTypes": ["java.lang.Long"], "doc": " 获取面试完成率统计\n\n @param jobId 岗位ID（可选）\n @return 完成率统计\n"}, {"name": "getAverageScores", "paramTypes": ["java.lang.Long"], "doc": " 获取平均分数统计\n\n @param jobId 岗位ID（可选）\n @return 平均分数统计\n"}, {"name": "recordInterviewEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 记录面试事件\n\n @param sessionId 会话ID\n @param eventType 事件类型\n @param eventData 事件数据\n"}, {"name": "cleanExpiredStats", "paramTypes": ["java.time.LocalDateTime"], "doc": " 清理过期统计数据\n\n @param beforeTime 清理时间点之前的数据\n @return 清理的记录数\n"}], "constructors": []}