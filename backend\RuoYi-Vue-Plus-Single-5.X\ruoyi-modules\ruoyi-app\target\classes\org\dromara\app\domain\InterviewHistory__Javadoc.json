{"doc": " 面试历史记录对象 app_interview_history\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [{"name": "id", "doc": " 历史记录ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "resultId", "doc": " 结果ID\n"}, {"name": "title", "doc": " 自定义标题\n"}, {"name": "isFavorite", "doc": " 是否收藏（0否 1是）\n"}, {"name": "tags", "doc": " 标签（JSON数组）\n"}, {"name": "notes", "doc": " 用户备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}