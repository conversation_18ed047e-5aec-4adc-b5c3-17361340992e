{"doc": " 任务队列管理服务接口\n 用于管理分析任务的队列和调度\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "startTaskProcessor", "paramTypes": [], "doc": " 启动任务队列处理器\n"}, {"name": "stopTaskProcessor", "paramTypes": [], "doc": " 停止任务队列处理器\n"}, {"name": "getQueueStatus", "paramTypes": [], "doc": " 获取队列状态\n\n @return 队列状态信息\n"}, {"name": "getPendingTasks", "paramTypes": ["int"], "doc": " 获取待处理任务列表\n\n @param limit 限制数量\n @return 任务列表\n"}, {"name": "getRunningTasks", "paramTypes": [], "doc": " 获取正在执行的任务列表\n\n @return 任务列表\n"}, {"name": "getCompletedTasks", "paramTypes": ["int"], "doc": " 获取已完成的任务列表\n\n @param limit 限制数量\n @return 任务列表\n"}, {"name": "cleanExpiredTasks", "paramTypes": [], "doc": " 清理过期任务\n\n @return 清理的任务数量\n"}, {"name": "retryFailedTask", "paramTypes": ["java.lang.String"], "doc": " 重试失败的任务\n\n @param taskId 任务ID\n @return 是否重试成功\n"}, {"name": "setMaxQueueCapacity", "paramTypes": ["int"], "doc": " 设置队列最大容量\n\n @param maxCapacity 最大容量\n"}, {"name": "setConcurrency", "paramTypes": ["int"], "doc": " 设置并发执行数量\n\n @param concurrency 并发数量\n"}, {"name": "pauseQueue", "paramTypes": [], "doc": " 暂停队列处理\n"}, {"name": "resumeQueue", "paramTypes": [], "doc": " 恢复队列处理\n"}, {"name": "isQueuePaused", "paramTypes": [], "doc": " 获取队列是否暂停\n\n @return 是否暂停\n"}], "constructors": []}