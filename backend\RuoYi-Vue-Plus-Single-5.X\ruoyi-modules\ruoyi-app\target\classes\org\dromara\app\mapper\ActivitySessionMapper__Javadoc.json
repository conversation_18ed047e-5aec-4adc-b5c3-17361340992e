{"doc": " 用户活动会话Mapper接口\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": " 根据会话ID查询活动会话\n\n @param sessionId 会话ID\n @return 活动会话\n"}, {"name": "selectActiveSessionsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户的活跃会话\n\n @param userId 用户ID\n @return 活跃会话列表\n"}, {"name": "updateSessionToInactive", "paramTypes": ["java.lang.String", "java.time.LocalDateTime", "java.lang.Long"], "doc": " 更新会话状态为非活跃\n\n @param sessionId 会话ID\n @param endTime   结束时间\n @param duration  持续时长\n @return 更新行数\n"}, {"name": "updateSessionDuration", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 更新会话持续时长\n\n @param sessionId 会话ID\n @param duration  持续时长\n @return 更新行数\n"}, {"name": "selectUserActivityHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": " 分页查询用户活动历史记录\n\n @param page         分页对象\n @param userId       用户ID\n @param activityType 活动类型\n @param startDate    开始时间\n @param endDate      结束时间\n @return 分页结果\n"}, {"name": "selectTodayDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 查询用户今日活动时长\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 今日活动时长(毫秒)\n"}, {"name": "selectWeekDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 查询用户本周活动时长\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 本周活动时长(毫秒)\n"}, {"name": "selectMonthDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 查询用户本月活动时长\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 本月活动时长(毫秒)\n"}, {"name": "selectTotalDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 查询用户总活动时长\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 总活动时长(毫秒)\n"}, {"name": "deleteUserActivityRecords", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 删除用户指定类型的活动记录\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 删除行数\n"}], "constructors": []}