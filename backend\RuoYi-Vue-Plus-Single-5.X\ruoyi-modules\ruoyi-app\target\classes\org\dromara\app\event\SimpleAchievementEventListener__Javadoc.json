{"doc": " 简化版成就事件监听器\n 专注于核心的成就检查功能\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserLoginEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.UserLoginEvent"], "doc": " 监听用户登录事件\n"}, {"name": "handleVideoWatchEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.VideoWatchEvent"], "doc": " 监听视频观看事件\n"}, {"name": "handleCommentEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.CommentEvent"], "doc": " 监听评论事件\n"}, {"name": "handleLikeEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.LikeEvent"], "doc": " 监听点赞事件\n"}, {"name": "handleStudyTimeEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.StudyTimeEvent"], "doc": " 监听学习时长事件\n"}], "constructors": []}