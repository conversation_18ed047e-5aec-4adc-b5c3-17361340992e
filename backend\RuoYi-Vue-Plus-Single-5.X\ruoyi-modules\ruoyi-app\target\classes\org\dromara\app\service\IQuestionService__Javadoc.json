{"doc": " 题目管理Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n\n @param questionId 题目主键\n @return 题目\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 题目分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 查询题目列表\n\n @param bo 查询条件\n @return 题目列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 新增题目\n\n @param bo 题目信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 修改题目\n\n @param bo 题目信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除题目信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 删除结果\n"}, {"name": "importQuestion", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 批量导入题目\n\n @param questionList 题目列表\n @param bankId 题库ID\n @param isUpdateSupport 是否支持更新\n @param operName 操作人员\n @return 导入结果信息\n"}, {"name": "exportQuestionList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 导出题目列表\n\n @param bo 查询条件\n @return 题目列表\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题目状态\n\n @param questionId 题目ID\n @param status 状态\n @return 更新结果\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题目状态\n\n @param questionIds 题目ID集合\n @param status  状态\n @return 更新结果\n"}, {"name": "updateDifficulty", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题目难度\n\n @param questionId 题目ID\n @param difficulty 难度\n @return 是否成功\n"}, {"name": "batchUpdateDifficulty", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题目难度\n\n @param questionIds 题目ID列表\n @param difficulty 难度\n @return 是否成功\n"}, {"name": "batchMoveToBank", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量移动题目到其他题库\n\n @param questionIds 题目ID集合\n @param targetBankId 目标题库ID\n @return 移动结果\n"}, {"name": "moveQuestion", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 移动题目到其他题库\n\n @param questionId 题目ID\n @param targetBankId 目标题库ID\n @return 是否成功\n"}, {"name": "batchMoveQuestions", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量移动题目到其他题库\n\n @param questionIds 题目ID列表\n @param targetBankId 目标题库ID\n @return 是否成功\n"}, {"name": "copyQuestion", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 复制题目\n\n @param questionId 源题目ID\n @param bankId 目标题库ID\n @return 复制结果\n"}, {"name": "batchCopyQuestion", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量复制题目\n\n @param questionIds 题目ID集合\n @param targetBankId 目标题库ID\n @return 复制结果\n"}, {"name": "batchCopyQuestions", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量复制题目\n\n @param questionIds 题目ID列表\n @param targetBankId 目标题库ID\n @return 是否成功\n"}, {"name": "batchSetSort", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 批量设置题目排序\n\n @param questionIds 题目ID列表\n @param sorts 排序列表\n @return 是否成功\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题目统计信息\n\n @param questionId 题目ID\n @return 统计信息\n"}, {"name": "getTypeStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目类型统计\n\n @param bankId 题库ID（可选）\n @return 统计结果\n"}, {"name": "getDifficultyStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目难度统计\n\n @param bankId 题库ID（可选）\n @return 统计结果\n"}, {"name": "getRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 随机抽题\n\n @param bankId 题库ID\n @param count 数量\n @param questionType 题目类型（可选）\n @param difficulty 难度（可选）\n @return 题目列表\n"}, {"name": "getTagStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目标签统计\n\n @param bankId 题库ID（可选）\n @return 统计结果\n"}, {"name": "batchSetTags", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 批量设置题目标签\n\n @param questionIds 题目ID集合\n @param tags 标签列表\n @return 设置结果\n"}, {"name": "batchSetDifficulty", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 批量设置题目难度\n\n @param questionIds 题目ID集合\n @param difficulty 难度\n @return 设置结果\n"}, {"name": "batchSetCategory", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量设置题目分类\n\n @param questionIds 题目ID集合\n @param category 分类\n @return 设置结果\n"}, {"name": "updatePracticeStatistics", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 更新题目练习统计\n\n @param questionId 题目ID\n @param isCorrect 是否答对\n @return 更新结果\n"}, {"name": "getBankQuestionStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库下的题目统计\n\n @param bankId 题库ID\n @return 统计信息\n"}, {"name": "aiScoreQuestion", "paramTypes": ["java.lang.Long"], "doc": " 题目AI评分\n\n @param questionId 题目ID\n @return 是否成功\n"}, {"name": "batchAiScoreQuestions", "paramTypes": ["java.util.List"], "doc": " 批量AI评分\n\n @param questionIds 题目ID列表\n @return 是否成功\n"}, {"name": "queryByBankId", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID查询题目列表\n\n @param bankId 题库ID\n @param bo 查询条件\n @param pageQuery 分页条件\n @return 题目列表\n"}], "constructors": []}