{"doc": " 聊天会话Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserSessions", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.Integer"], "doc": " 分页查询用户会话列表\n\n @param page   分页对象\n @param userId 用户ID\n @param status 会话状态（可选）\n @return 会话分页结果\n"}, {"name": "selectByIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据会话ID和用户ID查询会话详情\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 会话详情\n"}, {"name": "updateSessionStats", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String", "java.lang.Long"], "doc": " 更新会话消息统计\n\n @param sessionId      会话ID\n @param messageCount   消息数量\n @param lastMessage    最后消息内容\n @param lastActiveTime 最后活跃时间\n @return 影响行数\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 更新会话标题\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param title     新标题\n @return 影响行数\n"}, {"name": "updateSessionStatus", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": " 归档/取消归档会话\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param status    状态（0-归档，1-活跃）\n @return 影响行数\n"}, {"name": "getUserChatStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户聊天统计信息\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "deleteByIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 删除用户会话（软删除）\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 影响行数\n"}, {"name": "getPopularAgentTypes", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门Agent类型统计\n\n @param limit 返回数量限制\n @return Agent使用统计\n"}, {"name": "selectActiveSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户活跃会话列表\n\n @param userId 用户ID\n @param limit  数量限制\n @return 活跃会话列表\n"}, {"name": "selectUserSessionsByAgent", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": " 根据Agent类型查询用户会话\n\n @param userId    用户ID\n @param agentType Agent类型\n @param limit     数量限制\n @return 会话列表\n"}, {"name": "countUserSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 统计用户会话数量\n\n @param userId 用户ID\n @param status 会话状态（可选）\n @return 会话数量\n"}, {"name": "selectRecentSessions", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 查询最近活跃的会话\n\n @param userId    用户ID\n @param timestamp 时间戳阈值\n @return 最近活跃会话列表\n"}, {"name": "updateLastActiveTime", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 更新会话最后活跃时间\n\n @param sessionId      会话ID\n @param lastActiveTime 最后活跃时间\n @return 影响行数\n"}, {"name": "batchUpdateSessionStatus", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Integer"], "doc": " 批量更新会话状态\n\n @param sessionIds 会话ID列表\n @param userId     用户ID\n @param status     新状态\n @return 影响行数\n"}, {"name": "selectSessionStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户各Agent类型的会话统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "archiveInactiveSessions", "paramTypes": ["java.lang.Long"], "doc": " 清理长时间未活跃的会话（自动归档）\n\n @param threshold 时间阈值\n @return 影响行数\n"}, {"name": "resetSessionStats", "paramTypes": ["java.lang.String"], "doc": " 重置会话统计信息\n\n @param sessionId 会话ID\n @return 影响行数\n"}, {"name": "updateTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 更新会话标题\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param title     新标题\n @return 影响行数\n"}, {"name": "updateArchiveStatus", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 更新归档状态\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param archived  归档状态\n @return 影响行数\n"}, {"name": "countUserSessions", "paramTypes": ["java.lang.Long"], "doc": " 统计用户会话总数\n\n @param userId 用户ID\n @return 会话总数\n"}, {"name": "countActiveSessions", "paramTypes": ["java.lang.Long"], "doc": " 统计活跃会话数\n\n @param userId 用户ID\n @return 活跃会话数\n"}, {"name": "countSessionsByAgent", "paramTypes": ["java.lang.Long"], "doc": " 按Agent类型统计会话\n\n @param userId 用户ID\n @return 统计结果\n"}], "constructors": []}