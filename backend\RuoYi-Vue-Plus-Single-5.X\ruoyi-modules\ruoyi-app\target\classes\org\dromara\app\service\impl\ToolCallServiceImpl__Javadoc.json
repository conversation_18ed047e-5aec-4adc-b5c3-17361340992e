{"doc": " 工具调用服务实现\n\n <AUTHOR>\n", "fields": [{"name": "toolExecutors", "doc": " 工具执行器注册表\n"}], "enumConstants": [], "methods": [{"name": "initToolExecutors", "paramTypes": [], "doc": " 初始化方法，自动注册所有工具执行器\n"}, {"name": "registerToolExecutor", "paramTypes": ["org.dromara.app.service.tool.ToolExecutor"], "doc": " 注册工具执行器\n"}, {"name": "getRegisteredExecutors", "paramTypes": [], "doc": " 获取已注册的工具执行器列表\n"}, {"name": "getToolExecutor", "paramTypes": ["java.lang.String"], "doc": " 获取工具执行器\n"}, {"name": "isExecutorRegistered", "paramTypes": ["java.lang.String"], "doc": " 检查工具执行器是否已注册\n"}, {"name": "getToolExecutionStats", "paramTypes": [], "doc": " 获取工具执行统计信息\n"}, {"name": "reloadToolExecutors", "paramTypes": [], "doc": " 重新加载工具执行器\n"}], "constructors": []}