{"doc": " 面试书籍Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryBookPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 分页查询书籍列表\n\n @param pageNum     页码\n @param pageSize    每页大小\n @param category    分类筛选\n @param searchQuery 搜索关键词\n @param userId      用户ID\n @return 分页结果\n"}, {"name": "queryBookDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据ID查询书籍详情\n\n @param id     书籍ID\n @param userId 用户ID\n @return 书籍详情\n"}, {"name": "queryHotBooks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门书籍列表\n\n @param limit 限制数量\n @return 热门书籍列表\n"}, {"name": "queryRecommended<PERSON>ooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询推荐书籍列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 推荐书籍列表\n"}, {"name": "queryCategoryStats", "paramTypes": [], "doc": " 查询分类统计信息\n\n @return 分类统计\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": " 增加书籍阅读次数\n\n @param bookId 书籍ID\n @return 是否成功\n"}, {"name": "insertBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 新增书籍\n\n @param book 书籍信息\n @return 是否成功\n"}, {"name": "updateBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 修改书籍\n\n @param book 书籍信息\n @return 是否成功\n"}, {"name": "deleteBooks", "paramTypes": ["java.util.List"], "doc": " 删除书籍\n\n @param ids 书籍ID列表\n @return 是否成功\n"}, {"name": "updateBookStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 上架/下架书籍\n\n @param id     书籍ID\n @param status 状态\n @return 是否成功\n"}], "constructors": []}