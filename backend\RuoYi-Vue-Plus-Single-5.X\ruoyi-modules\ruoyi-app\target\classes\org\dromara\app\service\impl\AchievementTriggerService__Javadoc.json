{"doc": " 成就触发服务，用于监听系统事件并触发成就\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserLogin", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserLoginEvent"], "doc": " 用户登录事件处理\n\n @param event 登录事件\n"}, {"name": "handleInterviewCompleted", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.InterviewCompletedEvent"], "doc": " 用户完成面试事件处理\n\n @param event 面试完成事件\n"}, {"name": "handleUserLearn", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserLearnEvent"], "doc": " 用户学习事件处理\n\n @param event 学习事件\n"}, {"name": "handleConsecutiveLogin", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.ConsecutiveLoginEvent"], "doc": " 用户连续登录事件处理\n\n @param event 连续登录事件\n"}, {"name": "handleAbilityImprove", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.AbilityImproveEvent"], "doc": " 能力提升事件处理\n\n @param event 能力提升事件\n"}, {"name": "handleUserShare", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserShareEvent"], "doc": " 用户分享事件处理\n\n @param event 分享事件\n"}, {"name": "handleResumeSubmit", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.ResumeSubmitEvent"], "doc": " 简历提交事件处理\n\n @param event 简历提交事件\n"}], "constructors": []}