{"doc": " 聊天会话对象 app_chat_session\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "title", "doc": " 会话标题\n"}, {"name": "agentType", "doc": " Agent类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide\n"}, {"name": "messageCount", "doc": " 消息总数\n"}, {"name": "lastMessage", "doc": " 最后一条消息内容预览\n"}, {"name": "lastActiveTime", "doc": " 最后活跃时间\n"}, {"name": "status", "doc": " 会话状态：0-已归档，1-活跃\n"}, {"name": "sessionConfig", "doc": " 会话配置（JSON格式）\n"}, {"name": "messages", "doc": " 消息列表（不存储到数据库，用于返回给前端）\n"}, {"name": "stats", "doc": " 会话统计信息（不存储到数据库）\n"}, {"name": "delFlag", "doc": " 删除标志（0-正常，1-删除）\n"}], "enumConstants": [], "methods": [], "constructors": []}