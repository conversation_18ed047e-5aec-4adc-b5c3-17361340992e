{"doc": " 知识库业务对象 app_knowledge_base\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 知识库ID\n"}, {"name": "name", "doc": " 知识库名称\n"}, {"name": "description", "doc": " 知识库描述\n"}, {"name": "type", "doc": " 知识库类型 (general/technical/business/etc.)\n"}, {"name": "status", "doc": " 知识库状态 (0=禁用 1=启用)\n"}, {"name": "vectorDimension", "doc": " 向量维度 (默认1024)\n"}, {"name": "indexConfig", "doc": " 索引配置 (JSON格式)\n"}, {"name": "extendConfig", "doc": " 扩展配置 (JSON格式)\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "keyword", "doc": " 搜索关键词（用于名称和描述的模糊查询）\n"}, {"name": "types", "doc": " 知识库类型列表（用于多选过滤）\n"}, {"name": "statuses", "doc": " 状态列表（用于多选过滤）\n"}, {"name": "createTimeStart", "doc": " 创建时间范围 - 开始时间\n"}, {"name": "createTimeEnd", "doc": " 创建时间范围 - 结束时间\n"}, {"name": "updateTimeStart", "doc": " 更新时间范围 - 开始时间\n"}, {"name": "updateTimeEnd", "doc": " 更新时间范围 - 结束时间\n"}, {"name": "documentCountMin", "doc": " 文档数量范围 - 最小值\n"}, {"name": "documentCountMax", "doc": " 文档数量范围 - 最大值\n"}, {"name": "vectorCountMin", "doc": " 向量数量范围 - 最小值\n"}, {"name": "vectorCountMax", "doc": " 向量数量范围 - 最大值\n"}], "enumConstants": [], "methods": [], "constructors": []}