{"doc": " Ollama服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "chat", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.Double", "java.lang.Integer"], "doc": " 发送聊天消息（同步）\n\n @param model       模型名称\n @param messages    消息列表\n @param temperature 温度参数\n @param maxTokens   最大token数\n @return 响应结果\n"}, {"name": "chatStream", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.Double", "java.lang.Integer", "org.springframework.web.servlet.mvc.method.annotation.SseEmitter"], "doc": " 发送聊天消息（流式）\n\n @param model       模型名称\n @param messages    消息列表\n @param temperature 温度参数\n @param maxTokens   最大token数\n @param sseEmitter  SSE发射器\n"}, {"name": "getAvailableModels", "paramTypes": [], "doc": " 获取可用模型列表\n\n @return 模型列表\n"}, {"name": "isModelAvailable", "paramTypes": ["java.lang.String"], "doc": " 检查模型是否可用\n\n @param modelName 模型名称\n @return 是否可用\n"}, {"name": "getModelInfo", "paramTypes": ["java.lang.String"], "doc": " 获取模型信息\n\n @param modelName 模型名称\n @return 模型信息\n"}, {"name": "checkServiceStatus", "paramTypes": [], "doc": " 检查Ollama服务状态\n\n @return 服务状态\n"}], "constructors": []}