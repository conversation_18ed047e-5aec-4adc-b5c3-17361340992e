{"doc": " 数据库性能指标视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "databaseVersion", "doc": " 数据库版本\n"}, {"name": "uptime", "doc": " 数据库运行时间（秒）\n"}, {"name": "totalQueries", "doc": " 总查询数\n"}, {"name": "queriesPerSecond", "doc": " 每秒查询数（QPS）\n"}, {"name": "slowQueryCount", "doc": " 慢查询数量\n"}, {"name": "slowQueryRate", "doc": " 慢查询比例（百分比）\n"}, {"name": "averageQueryTime", "doc": " 平均查询时间（毫秒）\n"}, {"name": "connectionStats", "doc": " 连接数统计\n"}, {"name": "bufferPoolHitRate", "doc": " 缓冲池命中率（百分比）\n"}, {"name": "indexUsageRate", "doc": " 索引使用率（百分比）\n"}, {"name": "tableLockWaits", "doc": " 表锁等待次数\n"}, {"name": "rowLockWaits", "doc": " 行锁等待次数\n"}, {"name": "deadlockCount", "doc": " 死锁次数\n"}, {"name": "tempTableCount", "doc": " 临时表创建次数\n"}, {"name": "tempTableOnDiskCount", "doc": " 磁盘临时表创建次数\n"}, {"name": "databaseSize", "doc": " 数据库大小（MB）\n"}, {"name": "tablespaceUsage", "doc": " 表空间使用情况\n"}, {"name": "busiestTables", "doc": " 最繁忙的表\n"}, {"name": "maxConnections", "doc": " 最大连接数\n"}, {"name": "currentConnections", "doc": " 当前连接数\n"}, {"name": "connectionUsageRate", "doc": " 连接使用率（百分比）\n"}, {"name": "memoryUsage", "doc": " 内存使用情况\n"}, {"name": "diskIOStats", "doc": " 磁盘I/O统计\n"}, {"name": "performanceStatus", "doc": " 性能状态：excellent/good/fair/poor\n"}, {"name": "performanceScore", "doc": " 性能评分（0-100）\n"}, {"name": "optimizationSuggestions", "doc": " 优化建议\n"}, {"name": "statisticsTime", "doc": " 统计时间\n"}], "enumConstants": [], "methods": [], "constructors": []}