{"doc": " 成就管理Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询成就\n\n @param id 成就主键\n @return 成就\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成就列表\n\n @param pageQuery 分页查询条件\n @return 成就集合\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": " 查询成就列表\n\n @param achievementBo 成就查询条件\n @return 成就集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": " 新增成就\n\n @param achievementBo 成就\n @return 结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": " 修改成就\n\n @param achievementBo 成就\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除成就信息\n\n @param ids     需要删除的成就主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新成就状态\n\n @param ids      成就ID列表\n @param isActive 是否激活\n @return 操作结果\n"}, {"name": "copyAchievement", "paramTypes": ["java.lang.Long"], "doc": " 复制成就\n\n @param id 成就ID\n @return 操作结果\n"}, {"name": "previewTriggerCondition", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 预览成就触发条件\n\n @param triggerCondition 触发条件JSON\n @param userId           测试用户ID\n @return 预览结果\n"}, {"name": "testAchievementRule", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Object"], "doc": " 测试成就规则\n\n @param id        成就ID\n @param userId    测试用户ID\n @param eventType 事件类型\n @param eventData 事件数据\n @return 测试结果\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": " 获取成就统计信息\n\n @return 统计信息\n"}, {"name": "importAchievement", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 导入成就数据\n\n @param achievementList 成就数据列表\n @param isUpdateSupport 是否支持更新\n @param operName        操作用户\n @return 导入结果\n"}, {"name": "exportAchievement", "paramTypes": ["java.util.List"], "doc": " 导出成就数据\n\n @param list 成就数据列表\n @return 导出结果\n"}], "constructors": []}