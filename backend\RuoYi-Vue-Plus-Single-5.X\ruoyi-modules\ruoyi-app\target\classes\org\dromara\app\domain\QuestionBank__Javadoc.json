{"doc": " 题库实体\n\n <AUTHOR>\n", "fields": [{"name": "bankId", "doc": " 题库ID\n"}, {"name": "bankCode", "doc": " 题库编码\n"}, {"name": "title", "doc": " 题库标题\n"}, {"name": "description", "doc": " 题库描述\n"}, {"name": "majorId", "doc": " 专业ID\n"}, {"name": "icon", "doc": " 题库图标\n"}, {"name": "color", "doc": " 题库颜色\n"}, {"name": "difficulty", "doc": " 难度（1-简单 2-中等 3-困难）\n"}, {"name": "totalQuestions", "doc": " 题目总数\n"}, {"name": "practiceCount", "doc": " 练习次数\n"}, {"name": "categories", "doc": " 分类标签（JSON格式）\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "isBookmarked", "doc": " 是否收藏（非数据库字段）\n"}, {"name": "progress", "doc": " 学习进度（非数据库字段）\n"}], "enumConstants": [], "methods": [], "constructors": []}