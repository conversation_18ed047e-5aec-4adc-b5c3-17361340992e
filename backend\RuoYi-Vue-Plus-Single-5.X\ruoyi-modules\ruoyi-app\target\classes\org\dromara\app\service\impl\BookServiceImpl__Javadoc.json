{"doc": " 面试书籍Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryBookPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 分页查询书籍列表\n"}, {"name": "queryBookDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据ID查询书籍详情\n"}, {"name": "queryHotBooks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门书籍列表\n"}, {"name": "queryRecommended<PERSON>ooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询推荐书籍列表\n"}, {"name": "queryCategoryStats", "paramTypes": [], "doc": " 查询分类统计信息\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": " 增加书籍阅读次数\n"}, {"name": "insertBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 新增书籍\n"}, {"name": "updateBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 修改书籍\n"}, {"name": "deleteBooks", "paramTypes": ["java.util.List"], "doc": " 删除书籍\n"}, {"name": "updateBookStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 上架/下架书籍\n"}, {"name": "processBookData", "paramTypes": ["org.dromara.app.domain.Book"], "doc": " 处理书籍数据（转换标签等）\n"}], "constructors": []}