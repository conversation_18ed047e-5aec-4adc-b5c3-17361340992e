{"doc": " 问题分析Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询问题分析列表\n\n @param resultId 结果ID\n @return 问题分析列表\n"}, {"name": "selectByResultIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据结果ID和问题ID查询问题分析\n\n @param resultId   结果ID\n @param questionId 问题ID\n @return 问题分析\n"}, {"name": "selectByResultIdAndCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据分类查询问题分析列表\n\n @param resultId 结果ID\n @param category 分类\n @return 问题分析列表\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入问题分析\n\n @param questionAnalyses 问题分析列表\n @return 插入数量\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除问题分析\n\n @param resultId 结果ID\n @return 删除数量\n"}, {"name": "selectAvgScoreByUserIdAndCategory", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 查询用户在某分类问题上的平均分数\n\n @param userId   用户ID\n @param category 分类\n @return 平均分数\n"}, {"name": "selectAvgTimeSpentByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户答题时间统计\n\n @param userId 用户ID\n @return 平均答题时间\n"}, {"name": "selectHighScoreAnalyses", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 查询高分问题分析（用于学习参考）\n\n @param category 分类\n @param minScore 最低分数\n @param limit    限制数量\n @return 问题分析列表\n"}], "constructors": []}