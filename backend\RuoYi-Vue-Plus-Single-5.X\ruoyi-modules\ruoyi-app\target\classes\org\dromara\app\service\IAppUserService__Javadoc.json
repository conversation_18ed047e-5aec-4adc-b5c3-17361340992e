{"doc": " 应用用户Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByPhone", "paramTypes": ["java.lang.String"], "doc": " 根据手机号查询用户\n"}, {"name": "selectByEmail", "paramTypes": ["java.lang.String"], "doc": " 根据邮箱查询用户\n"}, {"name": "checkPhoneExists", "paramTypes": ["java.lang.String"], "doc": " 检查手机号是否存在\n"}, {"name": "checkEmailExists", "paramTypes": ["java.lang.String"], "doc": " 检查邮箱是否存在\n"}, {"name": "register", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 用户注册\n"}, {"name": "loginByPhone", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 手机号登录\n"}, {"name": "loginByPassword", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 密码登录\n"}, {"name": "resetPassword", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 重置密码\n"}, {"name": "getUserInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID获取用户信息\n"}, {"name": "updateLoginInfo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新最后登录信息\n"}], "constructors": []}