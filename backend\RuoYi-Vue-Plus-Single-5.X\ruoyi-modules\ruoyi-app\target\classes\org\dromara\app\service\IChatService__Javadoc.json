{"doc": " 聊天服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "java.lang.Long"], "doc": " 发送消息（同步响应）\n\n @param request 聊天请求\n @param userId  用户ID\n @return 聊天响应\n"}, {"name": "sendMessageStream", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "java.lang.Long"], "doc": " 发送消息（流式响应）\n\n @param request 聊天请求\n @param userId  用户ID\n @return SSE流对象\n"}, {"name": "createSession", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 创建新会话\n\n @param userId    用户ID\n @param agentType 代理类型\n @param title     会话标题（可选）\n @return 会话信息\n"}, {"name": "getUserSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取用户会话列表\n\n @param userId   用户ID\n @param pageNum  页码\n @param pageSize 每页大小\n @return 会话分页结果\n"}, {"name": "getSessionDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取会话详情（包含消息列表）\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 会话详情\n"}, {"name": "getSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取会话消息列表\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param pageNum   页码\n @param pageSize  每页大小\n @return 消息分页结果\n"}, {"name": "deleteSession", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 删除会话\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 是否成功\n"}, {"name": "clearSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 清空会话消息\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 是否成功\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 更新会话标题\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param title     新标题\n @return 是否成功\n"}, {"name": "archiveSession", "paramTypes": ["java.lang.String", "java.lang.Long", "boolean"], "doc": " 归档/取消归档会话\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param archived  是否归档\n @return 是否成功\n"}, {"name": "getUserChatStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户聊天统计信息\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "checkSessionPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查会话权限\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 是否有权限\n"}, {"name": "generateSessionTitle", "paramTypes": ["java.lang.String"], "doc": " 生成会话标题（基于首条消息）\n\n @param firstMessage 首条消息内容\n @return 生成的标题\n"}], "constructors": []}