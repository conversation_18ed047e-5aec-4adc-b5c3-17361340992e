{"doc": " 评估问题选项Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectOptionsByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据问题ID查询选项列表\n\n @param questionId 问题ID\n @return 选项列表\n"}, {"name": "selectOptionByQuestionIdAndCode", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据问题ID和选项编码查询选项\n\n @param questionId 问题ID\n @param optionCode 选项编码\n @return 选项\n"}, {"name": "insertBatch", "paramTypes": ["java.util.List"], "doc": " 批量插入选项\n\n @param options 选项列表\n @return 插入数量\n"}, {"name": "deleteByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据问题ID删除选项\n\n @param questionId 问题ID\n @return 删除数量\n"}], "constructors": []}