{"doc": " 多模态分析服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "calculateClarity", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": " 计算语音清晰度\n"}, {"name": "calculateFluency", "paramTypes": ["org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": " 计算语音流利度\n"}, {"name": "calculateConfidence", "paramTypes": ["org.dromara.app.service.IXunfeiService.VoiceEmotionResult"], "doc": " 计算自信度\n"}, {"name": "calculatePace", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": " 计算语速\n"}, {"name": "calculateAudioOverallScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult"], "doc": " 计算音频总体评分\n"}, {"name": "generateAudioInsights", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult"], "doc": " 生成音频分析洞察\n"}, {"name": "extractTechnicalMetrics", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 提取技术指标\n"}, {"name": "calculateSignalToNoiseRatio", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 计算信噪比\n"}, {"name": "count<PERSON>auses", "paramTypes": ["java.lang.String"], "doc": " 统计停顿次数\n"}, {"name": "countRepetitions", "paramTypes": ["java.lang.String"], "doc": " 统计重复次数\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 统计填充词数量\n"}, {"name": "analyzeAudioQuality", "paramTypes": ["byte[]"], "doc": " 分析音频质量\n"}, {"name": "analyzeEyeContact", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 分析眼神交流\n"}, {"name": "analyzePosture", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 分析姿态\n"}, {"name": "analyzeExpressions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 分析表情\n"}, {"name": "analyzeGestures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 分析手势\n"}, {"name": "calculateVideoOverallScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult"], "doc": " 计算视频总体评分\n"}, {"name": "detectEmotions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 检测情绪\n"}, {"name": "countGestures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 统计手势\n"}, {"name": "identifyPostureIssues", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult"], "doc": " 识别姿态问题\n"}, {"name": "extractFaceMetrics", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 提取面部指标\n"}, {"name": "assessProfessionalKnowledge", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 评估专业知识水平\n"}, {"name": "assessProfessionalKnowledgeByKeywords", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 基于关键词评估专业知识\n"}, {"name": "analyzeLogicalThinking", "paramTypes": ["java.lang.String"], "doc": " 分析逻辑思维能力\n"}, {"name": "assessInnovation", "paramTypes": ["java.lang.String"], "doc": " 评估创新能力\n"}, {"name": "calculateSkillMatching", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 计算技能匹配度\n"}, {"name": "analyzeStarStructure", "paramTypes": ["java.lang.String"], "doc": " 分析STAR结构\n"}, {"name": "extractKeywords", "paramTypes": ["java.lang.String"], "doc": " 提取关键词\n"}, {"name": "identifySkills", "paramTypes": ["java.lang.String"], "doc": " 识别技能\n"}, {"name": "calculateTopicRelevance", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 计算话题相关性\n"}, {"name": "generateTextImprovementSuggestions", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": " 生成文本改进建议\n"}, {"name": "getProfessionalKeywords", "paramTypes": ["java.lang.String"], "doc": " 获取专业关键词\n"}, {"name": "getRequiredSkills", "paramTypes": ["java.lang.String"], "doc": " 获取必需技能\n"}, {"name": "getTopicKeywords", "paramTypes": ["java.lang.String"], "doc": " 获取话题关键词\n"}, {"name": "generateOverallAssessment", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult", "java.lang.String"], "doc": " 生成综合评估\n"}, {"name": "calculateTotalScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": " 计算总分\n"}, {"name": "determineLevel", "paramTypes": ["int"], "doc": " 确定等级\n"}, {"name": "calculatePercentile", "paramTypes": ["int"], "doc": " 计算百分位数\n"}, {"name": "identifyStrengths", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": " 识别优势\n"}, {"name": "identifyWeaknesses", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": " 识别劣势\n"}, {"name": "generateDimensionScores", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": " 生成维度评分\n"}, {"name": "calculateDimensionPercentile", "paramTypes": ["int"], "doc": " 计算维度百分位数\n"}, {"name": "generateOverallFeedback", "paramTypes": ["int", "java.lang.String", "java.lang.String"], "doc": " 生成总体反馈\n"}, {"name": "generateComparisonData", "paramTypes": ["int", "java.lang.String"], "doc": " 生成对比数据\n"}], "constructors": []}