{"doc": " 用户书籍阅读记录对象 app_book_reading_record\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 记录ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "bookId", "doc": " 书籍ID\n"}, {"name": "currentChapterId", "doc": " 当前章节ID（MongoDB中的ID）\n"}, {"name": "currentChapterIndex", "doc": " 当前章节索引\n"}, {"name": "readingProgress", "doc": " 阅读进度百分比\n"}, {"name": "totalReadingTime", "doc": " 总阅读时长（分钟）\n"}, {"name": "lastReadingTime", "doc": " 最后阅读时间\n"}, {"name": "readingSettings", "doc": " 阅读设置（字体大小、主题等）\n"}, {"name": "completedChapters", "doc": " 已完成章节ID列表，逗号分隔\n"}, {"name": "isFinished", "doc": " 是否读完：0-未读完，1-已读完\n"}, {"name": "readingSettingsMap", "doc": " 阅读设置对象（转换后的字段，不存储到数据库）\n"}, {"name": "completedChapterList", "doc": " 已完成章节列表（转换后的字段，不存储到数据库）\n"}, {"name": "book", "doc": " 书籍信息（关联查询，不存储到数据库）\n"}], "enumConstants": [], "methods": [], "constructors": []}