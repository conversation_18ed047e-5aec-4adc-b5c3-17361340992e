{"doc": " 能力评估服务实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAssessmentQuestions", "paramTypes": [], "doc": " 获取评估问题列表\n"}, {"name": "convertToQuestionVo", "paramTypes": ["org.dromara.app.domain.AssessmentQuestion"], "doc": " 转换评估问题实体为VO对象\n"}, {"name": "submitAssessmentResults", "paramTypes": ["java.util.List"], "doc": " 提交评估结果\n"}, {"name": "createAssessmentRecord", "paramTypes": ["java.lang.Long", "int"], "doc": " 创建评估记录\n"}, {"name": "convertToAssessmentResult", "paramTypes": ["org.dromara.app.domain.bo.AssessmentResultBo", "java.lang.Long"], "doc": " 转换评估结果BO为实体对象\n"}, {"name": "calculateQuestionScore", "paramTypes": ["org.dromara.app.domain.AssessmentQuestion", "org.dromara.app.domain.bo.AssessmentResultBo"], "doc": " 计算单个问题的得分\n"}, {"name": "calculateAbilityScores", "paramTypes": ["java.util.Map"], "doc": " 计算各维度能力得分\n"}, {"name": "calculateCategoryScore", "paramTypes": ["java.util.List"], "doc": " 计算单个类别的得分\n"}, {"name": "updateRecordScores", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": " 更新评估记录的得分\n"}, {"name": "createOrUpdateUserGrowthProfile", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": " 创建或更新用户成长档案\n"}, {"name": "determineUserStage", "paramTypes": ["java.lang.Integer"], "doc": " 根据总分确定用户阶段\n"}, {"name": "setInitialAssessmentScores", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": " 设置初始评估分数\n"}, {"name": "setCurrentAssessmentScores", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": " 设置当前评估分数\n"}, {"name": "getDetailedAbilityReport", "paramTypes": [], "doc": " 获取详细能力报告\n"}, {"name": "generateStrengths", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": " 生成优势分析\n"}, {"name": "generateWeaknesses", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": " 生成劣势分析\n"}, {"name": "generateRecommendations", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": " 生成推荐内容\n"}, {"name": "getUserGrowthProfile", "paramTypes": [], "doc": " 获取用户成长档案\n"}, {"name": "convertToGrowthProfileVo", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile"], "doc": " 转换用户成长档案实体为VO对象\n"}, {"name": "parseJsonStringToList", "paramTypes": ["java.lang.String"], "doc": " 解析JSON字符串为字符串列表\n"}], "constructors": []}