{"doc": " 面试结果相关异常处理器\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleServiceException", "paramTypes": ["org.dromara.common.core.exception.ServiceException"], "doc": " 处理业务异常\n"}, {"name": "handleMethodArgumentNotValidException", "paramTypes": ["org.springframework.web.bind.MethodArgumentNotValidException"], "doc": " 处理参数验证异常（@Valid）\n"}, {"name": "handleBindException", "paramTypes": ["org.springframework.validation.BindException"], "doc": " 处理参数绑定异常\n"}, {"name": "handleConstraintViolationException", "paramTypes": ["jakarta.validation.ConstraintViolationException"], "doc": " 处理约束验证异常（@Validated）\n"}, {"name": "handleIllegalArgumentException", "paramTypes": ["java.lang.IllegalArgumentException"], "doc": " 处理非法参数异常\n"}, {"name": "handleNullPointerException", "paramTypes": ["java.lang.NullPointerException"], "doc": " 处理空指针异常\n"}, {"name": "handleDataAccessException", "paramTypes": ["java.lang.Exception"], "doc": " 处理数据库相关异常\n"}, {"name": "handleException", "paramTypes": ["java.lang.Exception"], "doc": " 处理其他未知异常\n"}], "constructors": []}