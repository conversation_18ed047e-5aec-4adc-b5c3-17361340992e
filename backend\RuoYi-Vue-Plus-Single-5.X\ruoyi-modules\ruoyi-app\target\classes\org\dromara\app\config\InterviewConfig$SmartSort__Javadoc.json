{"doc": " 智能排序权重配置\n", "fields": [{"name": "hotWeight", "doc": " 热度权重\n"}, {"name": "passRateWeight", "doc": " 通过率权重\n"}, {"name": "difficultyWeight", "doc": " 难度权重\n"}, {"name": "durationWeight", "doc": " 时长权重\n"}, {"name": "questionCountWeight", "doc": " 题目数量权重\n"}, {"name": "optimalPassRate", "doc": " 最佳通过率\n"}, {"name": "optimalDiff<PERSON>ulty", "doc": " 最佳难度\n"}, {"name": "optimalDuration", "doc": " 最佳时长\n"}, {"name": "optimalQuestionCount", "doc": " 最佳题目数量\n"}], "enumConstants": [], "methods": [], "constructors": []}