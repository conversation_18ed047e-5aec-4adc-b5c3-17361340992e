{"doc": " 用户徽章实体\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 用户徽章ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "badgeId", "doc": " 徽章ID\n"}, {"name": "unlocked", "doc": " 是否解锁\n"}, {"name": "unlockedAt", "doc": " 解锁时间\n"}, {"name": "isPinned", "doc": " 是否置顶\n"}, {"name": "pinnedAt", "doc": " 置顶时间\n"}, {"name": "pinnedSort", "doc": " 置顶排序\n"}, {"name": "notificationStatus", "doc": " 通知状态（0=未通知，1=已通知）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " 是否查看过\n"}, {"name": "viewedAt", "doc": " 查看时间\n"}, {"name": "unlockChannel", "doc": " 解锁渠道\n"}, {"name": "userAchievementId", "doc": " 关联用户成就ID\n"}, {"name": "extraData", "doc": " 额外数据（JSON格式）\n"}], "enumConstants": [], "methods": [], "constructors": []}