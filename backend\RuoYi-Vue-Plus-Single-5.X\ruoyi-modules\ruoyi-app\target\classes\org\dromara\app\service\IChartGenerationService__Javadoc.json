{"doc": " 图表生成服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateRadarChart", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 生成雷达图\n\n @param dimensionScores 维度评分列表\n @param title 图表标题\n @return 雷达图图像\n"}, {"name": "generateRadarChartWithComparison", "paramTypes": ["java.util.List", "java.util.List", "java.lang.String"], "doc": " 生成雷达图（带对比数据）\n\n @param dimensionScores 维度评分列表\n @param industryAverages 行业平均分\n @param title 图表标题\n @return 雷达图图像\n"}, {"name": "generateBarChart", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 生成柱状图\n\n @param data 数据\n @param title 图表标题\n @param xAxisLabel X轴标签\n @param yAxisLabel Y轴标签\n @return 柱状图图像\n"}, {"name": "generateLineChart", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 生成折线图\n\n @param data 数据\n @param title 图表标题\n @param xAxisLabel X轴标签\n @param yAxisLabel Y轴标签\n @return 折线图图像\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": " 生成饼图\n\n @param data 数据\n @param title 图表标题\n @return 饼图图像\n"}, {"name": "generateCapabilityDistributionChart", "paramTypes": ["java.util.List"], "doc": " 生成能力分布图\n\n @param dimensionScores 维度评分列表\n @return 能力分布图图像\n"}, {"name": "generateTrendChart", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": " 生成趋势分析图\n\n @param historicalData 历史数据\n @param title 图表标题\n @return 趋势分析图图像\n"}, {"name": "generateDashboard", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": " 生成综合评估仪表盘\n\n @param report 面试报告\n @return 仪表盘图像\n"}, {"name": "imageToByteArray", "paramTypes": ["java.awt.image.BufferedImage", "java.lang.String"], "doc": " 将图像转换为字节数组\n\n @param image 图像\n @param format 格式（PNG, JPEG等）\n @return 字节数组\n"}, {"name": "saveImageToFile", "paramTypes": ["java.awt.image.BufferedImage", "java.lang.String", "java.lang.String"], "doc": " 保存图像到文件\n\n @param image 图像\n @param filePath 文件路径\n @param format 格式\n @return 是否保存成功\n"}], "constructors": []}