{"doc": " 成就事件监听器\n 监听各种业务事件，自动触发成就检查和更新\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserRegistrationEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.UserRegistrationEvent"], "doc": " 监听用户注册事件\n"}, {"name": "handleLearningCompletedEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": " 监听学习完成事件\n"}, {"name": "handleInterviewCompletedEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": " 监听面试完成事件\n"}, {"name": "handleAbilityImproveEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": " 监听能力提升事件\n"}, {"name": "checkLearningAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": " 检查学习相关成就\n"}, {"name": "checkInterviewAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": " 检查面试相关成就\n"}, {"name": "checkAbilityAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": " 检查能力提升相关成就\n"}, {"name": "checkLearningCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": " 检查学习条件\n"}, {"name": "checkInterviewCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": " 检查面试条件\n"}, {"name": "checkAbilityCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": " 检查能力提升条件\n"}, {"name": "calculateLearningProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": " 计算学习进度\n"}, {"name": "calculateInterviewProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": " 计算面试进度\n"}, {"name": "calculateAbilityProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": " 计算能力提升进度\n"}], "constructors": []}