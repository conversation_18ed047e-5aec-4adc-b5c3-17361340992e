{"doc": " 题目评论VO\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 评论ID\n"}, {"name": "questionId", "doc": " 题目ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "author", "doc": " 用户昵称\n"}, {"name": "avatar", "doc": " 用户头像\n"}, {"name": "content", "doc": " 评论内容\n"}, {"name": "likes", "doc": " 点赞数\n"}, {"name": "replyCount", "doc": " 回复数\n"}, {"name": "parentId", "doc": " 父评论ID\n"}, {"name": "replies", "doc": " 回复列表\n"}, {"name": "time", "doc": " 发表时间\n"}, {"name": "liked", "doc": " 是否点赞(true:已点赞,false:未点赞)\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}