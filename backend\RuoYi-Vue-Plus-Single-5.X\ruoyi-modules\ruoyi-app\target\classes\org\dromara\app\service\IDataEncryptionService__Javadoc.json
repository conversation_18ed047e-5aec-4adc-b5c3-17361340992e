{"doc": " 数据加密服务接口\n 用于敏感数据的加密存储和解密访问\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "encrypt", "paramTypes": ["java.lang.String"], "doc": " 加密字符串\n\n @param plainText 明文\n @return 密文\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String"], "doc": " 解密字符串\n\n @param cipherText 密文\n @return 明文\n"}, {"name": "encrypt", "paramTypes": ["byte[]"], "doc": " 加密字节数组\n\n @param plainBytes 明文字节数组\n @return 密文字节数组\n"}, {"name": "decrypt", "paramTypes": ["byte[]"], "doc": " 解密字节数组\n\n @param cipherBytes 密文字节数组\n @return 明文字节数组\n"}, {"name": "batchEncrypt", "paramTypes": ["java.util.Map"], "doc": " 批量加密\n\n @param plainTexts 明文列表\n @return 密文列表\n"}, {"name": "batchDecrypt", "paramTypes": ["java.util.Map"], "doc": " 批量解密\n\n @param cipherTexts 密文列表\n @return 明文列表\n"}, {"name": "generateHash", "paramTypes": ["java.lang.String"], "doc": " 生成数据摘要（哈希）\n\n @param data 原始数据\n @return 数据摘要\n"}, {"name": "verifyHash", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证数据摘要\n\n @param data 原始数据\n @param hash 数据摘要\n @return 是否匹配\n"}, {"name": "generateRandomKey", "paramTypes": ["int"], "doc": " 生成随机密钥\n\n @param keyLength 密钥长度\n @return 密钥\n"}, {"name": "encryptFile", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 加密文件\n\n @param inputFilePath 输入文件路径\n @param outputFilePath 输出文件路径\n @return 是否成功\n"}, {"name": "decryptFile", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 解密文件\n\n @param inputFilePath 输入文件路径\n @param outputFilePath 输出文件路径\n @return 是否成功\n"}, {"name": "getEncryptionStatus", "paramTypes": [], "doc": " 获取加密状态\n\n @return 加密状态信息\n"}, {"name": "rotateEncryptionKey", "paramTypes": [], "doc": " 轮换加密密钥\n\n @return 是否成功\n"}, {"name": "verifyEncryptionIntegrity", "paramTypes": ["java.lang.String"], "doc": " 验证加密完整性\n\n @param data 数据\n @return 是否完整\n"}, {"name": "setEncryptionAlgorithm", "paramTypes": ["java.lang.String"], "doc": " 设置加密算法\n\n @param algorithm 算法名称\n"}, {"name": "getSupportedAlgorithms", "paramTypes": [], "doc": " 获取支持的加密算法列表\n\n @return 算法列表\n"}], "constructors": []}