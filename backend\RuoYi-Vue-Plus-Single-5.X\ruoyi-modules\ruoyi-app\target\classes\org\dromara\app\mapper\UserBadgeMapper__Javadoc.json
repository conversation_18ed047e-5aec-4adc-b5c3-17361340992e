{"doc": " 用户徽章数据层\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserBadges", "paramTypes": ["java.lang.String"], "doc": " 获取用户所有徽章\n\n @param userId 用户ID\n @return 徽章列表\n"}, {"name": "selectUserBadgeById", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户徽章详情\n\n @param userId  用户ID\n @param badgeId 徽章ID\n @return 徽章详情\n"}, {"name": "countPinnedBadges", "paramTypes": ["java.lang.String"], "doc": " 获取用户置顶徽章数量\n\n @param userId 用户ID\n @return 置顶徽章数量\n"}, {"name": "updatePinStatus", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.time.LocalDateTime"], "doc": " 更新徽章置顶状态\n\n @param userId   用户ID\n @param badgeId  徽章ID\n @param isPinned 是否置顶\n @param pinnedAt 置顶时间\n @return 更新行数\n"}, {"name": "selectPinnedBadges", "paramTypes": ["java.lang.String"], "doc": " 获取用户置顶徽章\n\n @param userId 用户ID\n @return 置顶徽章列表\n"}, {"name": "countTotalBadges", "paramTypes": [], "doc": " 获取总徽章数\n\n @return 总徽章数\n"}, {"name": "countUnlockedBadges", "paramTypes": ["java.lang.String"], "doc": " 获取用户已解锁徽章数\n\n @param userId 用户ID\n @return 已解锁徽章数\n"}, {"name": "selectByUserIdAndBadgeId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据用户ID和徽章ID获取用户徽章\n\n @param userId  用户ID\n @param badgeId 徽章ID\n @return 用户徽章\n"}, {"name": "selectRecentUnlockedBadges", "paramTypes": ["java.lang.String", "int"], "doc": " 获取用户最近解锁的徽章\n\n @param userId 用户ID\n @param limit  数量限制\n @return 最近解锁的徽章\n"}, {"name": "updateNotificationStatus", "paramTypes": ["java.lang.String", "int"], "doc": " 更新徽章通知状态\n\n @param userId 用户ID\n @param status 状态（0=未通知，1=已通知）\n @return 更新行数\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 标记徽章为已查看\n\n @param userId  用户ID\n @param badgeId 徽章ID\n @return 更新行数\n"}, {"name": "countUnlockedBadgesByCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户某类别已解锁徽章数\n\n @param userId   用户ID\n @param category 类别\n @return 已解锁徽章数\n"}, {"name": "countUnlockedBadgesByRarity", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户某稀有度已解锁徽章数\n\n @param userId 用户ID\n @param rarity 稀有度\n @return 已解锁徽章数\n"}], "constructors": []}