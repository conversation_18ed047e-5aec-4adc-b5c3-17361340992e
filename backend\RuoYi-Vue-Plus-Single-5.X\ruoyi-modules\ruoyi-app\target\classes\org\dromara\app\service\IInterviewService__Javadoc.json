{"doc": " 面试服务接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "getJobList", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": " 获取岗位列表\n\n @param queryBo 查询参数\n @return 岗位列表响应\n"}, {"name": "getCategories", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": " 获取岗位分类列表\n\n @param includeJobCount 是否包含岗位数量\n @return 分类列表响应\n"}, {"name": "getInterviewModes", "paramTypes": [], "doc": " 获取面试模式列表\n\n @return 面试模式列表响应\n"}, {"name": "getSearchSuggestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取搜索建议\n\n @param keyword 搜索关键词\n @param limit   返回数量限制\n @return 搜索建议响应\n"}, {"name": "createInterviewSession", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.util.List"], "doc": " 创建面试会话\n\n @param jobId               岗位ID\n @param mode                面试模式\n @param resumeUrl           简历URL\n @param customizedQuestions 自定义问题列表\n @return 创建会话响应\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 收藏/取消收藏岗位\n\n @param jobId       岗位ID\n @param isFavorited 收藏状态\n"}, {"name": "checkDevice", "paramTypes": [], "doc": " 设备检测\n\n @return 设备检测结果\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取统计信息\n\n @return 统计信息响应\n"}, {"name": "getInterviewHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取面试历史记录\n\n @param page     页码\n @param pageSize 每页大小\n @param category 分类筛选\n @param status   状态筛选\n @return 历史记录列表响应\n"}, {"name": "getUserStatistics", "paramTypes": [], "doc": " 获取用户统计数据\n\n @return 用户统计数据\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位详情\n\n @param jobId 岗位ID\n @return 岗位详情响应\n"}, {"name": "getSampleQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取示例问题\n\n @param jobId 岗位ID\n @param count 问题数量\n @return 示例问题响应\n"}, {"name": "getRelatedJobs", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取相关岗位\n\n @param jobId 当前岗位ID\n @param limit 数量限制\n @return 相关岗位响应\n"}, {"name": "getJobStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位统计数据\n\n @param jobId 岗位ID\n @return 岗位统计响应\n"}, {"name": "getJobInterviewModes", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位面试模式列表\n\n @param jobId 岗位ID\n @return 岗位面试模式响应\n"}, {"name": "checkUserReadiness", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 检查用户准备度\n\n @param jobId  岗位ID\n @param userId 用户ID\n @return 用户准备度响应\n"}, {"name": "shareJob", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 分享岗位信息\n\n @param jobId    岗位ID\n @param platform 分享平台\n @return 分享岗位响应\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": " 获取会话信息\n\n @param sessionId 会话ID\n @return 会话信息\n"}, {"name": "getSessionQuestions", "paramTypes": ["java.lang.String"], "doc": " 获取面试问题列表\n\n @param sessionId 会话ID\n @return 问题列表响应\n"}, {"name": "submitAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 提交面试回答\n\n @param sessionId  会话ID\n @param questionId 问题ID\n @param answer     回答内容\n @param audioUrl   音频URL\n @param duration   回答时长(秒)\n @return 回答响应\n"}, {"name": "endSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 结束面试会话\n\n @param sessionId 会话ID\n @param reason    结束原因\n @return 结束会话响应\n"}, {"name": "submitFeedback", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String", "java.util.List"], "doc": " 提交面试反馈\n\n @param sessionId 会话ID\n @param rating    评分\n @param comments  评论\n @param tags      标签\n @return 反馈响应\n"}, {"name": "getInterviewResult", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果\n\n @param sessionId 会话ID\n @return 面试结果\n"}, {"name": "checkDevices", "paramTypes": [], "doc": " 检查设备状态\n\n @return 设备状态映射\n"}, {"name": "getSessionStatus", "paramTypes": ["java.lang.String"], "doc": " 获取会话状态\n\n @param sessionId 会话ID\n @return 会话状态\n"}, {"name": "getInterviewerInfo", "paramTypes": ["java.lang.String"], "doc": " 获取AI面试官信息\n\n @param interviewerId 面试官ID\n @return 面试官信息\n"}], "constructors": []}