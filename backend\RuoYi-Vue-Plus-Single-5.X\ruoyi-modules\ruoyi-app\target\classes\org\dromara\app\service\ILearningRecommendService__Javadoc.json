{"doc": " 学习推荐服务接口\n 基于用户能力评估和学习历史，提供个性化的学习资源推荐\n\n <AUTHOR>\n @date 2025-07-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRecommendedVideos", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐视频列表\n 基于用户能力短板和学习偏好推荐视频课程\n\n @param userId      用户ID\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词\n @return 推荐视频列表\n"}, {"name": "getRecommendedQuestionBanks", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐题库列表\n 基于用户能力短板和练习历史推荐题库\n\n @param userId      用户ID\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词\n @return 推荐题库列表\n"}, {"name": "getRecommendedBooks", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐书籍列表\n 基于用户能力短板和阅读偏好推荐书籍\n\n @param userId      用户ID\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词\n @return 推荐书籍列表\n"}, {"name": "getUserCapabilities", "paramTypes": ["java.lang.Long"], "doc": " 获取用户能力评估数据\n 用于前端显示用户当前能力状况和薄弱环节\n\n @param userId 用户ID\n @return 用户能力评估数据\n"}, {"name": "getRecommendationStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取推荐统计信息\n 包括推荐总数、各类型推荐数量等统计信息\n\n @param userId 用户ID\n @return 推荐统计信息\n"}, {"name": "refreshUserRecommendations", "paramTypes": ["java.lang.Long"], "doc": " 刷新用户推荐\n 基于用户最新的学习行为和能力评估重新计算推荐\n\n @param userId 用户ID\n"}, {"name": "recordRecommendationFeedback", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 记录用户对推荐内容的反馈\n 用于优化推荐算法\n\n @param userId       用户ID\n @param resourceType 资源类型\n @param resourceId   资源ID\n @param action       用户行为\n"}, {"name": "calculateUserWeaknesses", "paramTypes": ["java.lang.Long"], "doc": " 计算用户能力短板\n 基于面试历史和学习记录分析用户薄弱环节\n\n @param userId 用户ID\n @return 能力短板列表\n"}, {"name": "calculateRecommendationPriority", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long"], "doc": " 计算资源推荐优先级\n 基于用户能力短板、资源质量、学习历史等因素计算推荐优先级\n\n @param userId       用户ID\n @param resourceType 资源类型\n @param resourceId   资源ID\n @return 推荐优先级分数\n"}, {"name": "getPersonalizedAlgorithmConfig", "paramTypes": ["java.lang.Long"], "doc": " 获取个性化推荐算法配置\n 根据用户学习偏好和历史行为调整推荐算法参数\n\n @param userId 用户ID\n @return 算法配置参数\n"}], "constructors": []}