{"doc": " 聊天消息Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectSessionMessages", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Long"], "doc": " 分页查询会话消息列表\n\n @param page      分页对象\n @param sessionId 会话ID\n @param userId    用户ID\n @return 消息分页结果\n"}, {"name": "selectRecentMessages", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": " 查询会话的最新N条消息（用于构建上下文）\n\n @param sessionId 会话ID\n @param userId    用户ID\n @param limit     消息数量限制\n @return 消息列表\n"}, {"name": "selectFirstUserMessage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询会话的第一条用户消息（用于生成标题）\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 第一条用户消息\n"}, {"name": "countSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 统计会话消息数量\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 消息数量\n"}, {"name": "deleteSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 清空会话消息（软删除）\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 影响行数\n"}, {"name": "updateMessageStatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 更新消息状态\n\n @param messageId 消息ID\n @param status    消息状态\n @return 影响行数\n"}, {"name": "markMessagesAsRead", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 标记消息为已读\n\n @param messageIds 消息ID列表\n @param userId     用户ID\n @return 影响行数\n"}, {"name": "countUnreadMessages", "paramTypes": ["java.lang.Long"], "doc": " 查询用户未读消息数量\n\n @param userId 用户ID\n @return 未读消息数量\n"}, {"name": "countMessagesByType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据消息类型统计数量\n\n @param userId      用户ID\n @param messageType 消息类型\n @return 消息数量\n"}, {"name": "batchInsertMessages", "paramTypes": ["java.util.List"], "doc": " 批量插入消息\n\n @param messages 消息列表\n @return 影响行数\n"}, {"name": "searchMessages", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": " 搜索消息内容\n\n @param userId    用户ID\n @param sessionId 会话ID（可选）\n @param keyword   搜索关键词\n @param startTime 开始时间（可选）\n @param endTime   结束时间（可选）\n @return 消息列表\n"}, {"name": "getMessageStats", "paramTypes": ["java.lang.Long", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": " 获取用户消息统计信息\n\n @param userId    用户ID\n @param startTime 开始时间（可选）\n @param endTime   结束时间（可选）\n @return 统计信息\n"}, {"name": "selectLastMessage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取会话中的最后一条消息\n\n @param sessionId 会话ID\n @param userId    用户ID\n @return 最后一条消息\n"}, {"name": "updateMessageError", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 更新消息错误信息\n\n @param messageId    消息ID\n @param errorMessage 错误信息\n @return 影响行数\n"}, {"name": "selectChildMessages", "paramTypes": ["java.lang.String"], "doc": " 根据父消息ID查询子消息\n\n @param parentMessageId 父消息ID\n @return 子消息列表\n"}, {"name": "countUserMessages", "paramTypes": ["java.lang.Long"], "doc": " 统计用户消息总数\n\n @param userId 用户ID\n @return 消息总数\n"}, {"name": "countTodayMessages", "paramTypes": ["java.lang.Long"], "doc": " 统计今日消息数\n\n @param userId 用户ID\n @return 今日消息数\n"}], "constructors": []}