{"doc": " 题目工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDifficultyDescription", "paramTypes": ["java.lang.Integer"], "doc": " 将难度代码转换为中文描述\n\n @param difficulty 难度代码（1-简单 2-中等 3-困难）\n @return 难度描述\n"}, {"name": "getDifficultyCode", "paramTypes": ["java.lang.String"], "doc": " 将难度描述转换为代码\n\n @param difficultyDescription 难度描述\n @return 难度代码\n"}, {"name": "getDifficultyEnum", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取难度枚举实例\n\n @param difficulty 难度代码\n @return 难度枚举实例\n"}, {"name": "getTypeDescription", "paramTypes": ["java.lang.Integer"], "doc": " 获取题目类型描述\n\n @param typeCode 类型代码\n @return 类型描述\n"}, {"name": "getTypeCode", "paramTypes": ["java.lang.String"], "doc": " 获取题目类型代码\n\n @param typeDescription 类型描述\n @return 类型代码\n"}, {"name": "getTypeEnum", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取题目类型枚举实例\n\n @param typeCode 类型代码\n @return 类型枚举实例\n"}, {"name": "calculateEstimatedTime", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 根据难度和题目类型计算预计完成时间\n\n @param difficulty 难度代码（1-简单 2-中等 3-困难）\n @param typeCode   题目类型代码\n @return 预计时间（分钟）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Integer"], "doc": " 验证难度代码是否有效\n\n @param difficulty 难度代码\n @return 是否有效\n"}, {"name": "isValidType", "paramTypes": ["java.lang.Integer"], "doc": " 验证题目类型代码是否有效\n\n @param typeCode 类型代码\n @return 是否有效\n"}, {"name": "getAllDifficulties", "paramTypes": [], "doc": " 获取所有难度选项\n\n @return 难度枚举数组\n"}, {"name": "getAllTypes", "paramTypes": [], "doc": " 获取所有题目类型选项\n\n @return 类型枚举数组\n"}], "constructors": []}