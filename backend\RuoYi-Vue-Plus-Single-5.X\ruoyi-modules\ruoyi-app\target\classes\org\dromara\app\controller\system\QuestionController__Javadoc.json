{"doc": " 题目管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目列表\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题目列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 新增题目\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 修改题目\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目\n"}, {"name": "listByBank", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID查询题目列表\n"}, {"name": "updateStatus", "paramTypes": ["java.util.Map"], "doc": " 更新题目状态\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.Map"], "doc": " 批量更新题目状态\n"}, {"name": "updateDifficulty", "paramTypes": ["java.util.Map"], "doc": " 更新题目难度\n"}, {"name": "batchUpdateDifficulty", "paramTypes": ["java.util.Map"], "doc": " 批量更新题目难度\n"}, {"name": "copyQuestion", "paramTypes": ["java.util.Map"], "doc": " 复制题目\n"}, {"name": "batchCopyQuestions", "paramTypes": ["java.util.Map"], "doc": " 批量复制题目\n"}, {"name": "moveQuestion", "paramTypes": ["java.util.Map"], "doc": " 移动题目到其他题库\n"}, {"name": "batchMoveQuestions", "paramTypes": ["java.util.Map"], "doc": " 批量移动题目到其他题库\n"}, {"name": "batchSetSort", "paramTypes": ["java.util.Map"], "doc": " 批量设置题目排序\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "boolean"], "doc": " 导入题目数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}, {"name": "getTypeStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目类型统计\n"}, {"name": "getDifficultyStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目难度统计\n"}, {"name": "getRandomQuestions", "paramTypes": ["java.util.Map"], "doc": " 随机抽题\n"}, {"name": "searchByTags", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据标签搜索题目\n"}, {"name": "getTagStats", "paramTypes": ["java.lang.Long"], "doc": " 获取题目标签统计\n"}, {"name": "aiScoreQuestion", "paramTypes": ["java.util.Map"], "doc": " 题目AI评分\n"}, {"name": "batchAiScoreQuestions", "paramTypes": ["java.util.Map"], "doc": " 批量AI评分\n"}], "constructors": []}