{"doc": " 面试问题Mapper接口\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByJobId", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据岗位ID查询问题列表\n\n @param jobId      岗位ID\n @param difficulty 难度等级\n @param limit      限制数量\n @return 问题列表\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域查询问题\n\n @param technicalDomain 技术领域\n @param questionType    问题类型\n @param limit          限制数量\n @return 问题列表\n"}, {"name": "selectMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询多模态问题\n\n @param jobId 岗位ID\n @param limit 限制数量\n @return 多模态问题列表\n"}, {"name": "selectByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 根据标签查询问题\n\n @param tags  标签列表\n @param limit 限制数量\n @return 问题列表\n"}, {"name": "selectQuestionPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": " 分页查询问题列表\n\n @param page           分页参数\n @param jobId          岗位ID\n @param questionType   问题类型\n @param difficulty     难度等级\n @param category       问题分类\n @return 分页结果\n"}, {"name": "selectByIds", "paramTypes": ["java.util.List"], "doc": " 根据问题ID批量查询\n\n @param ids 问题ID列表\n @return 问题列表\n"}, {"name": "count<PERSON>y<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询问题数量\n\n @param category 问题分类\n @return 问题数量\n"}, {"name": "countByJobId", "paramTypes": ["java.lang.Long"], "doc": " 根据岗位ID查询问题数量\n\n @param jobId 岗位ID\n @return 问题数量\n"}, {"name": "selectRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String", "java.lang.Integer"], "doc": " 查询随机问题\n\n @param jobId        岗位ID\n @param difficulty   难度等级\n @param questionType 问题类型\n @param limit        限制数量\n @return 随机问题列表\n"}, {"name": "selectQuestionStatistics", "paramTypes": [], "doc": " 查询问题统计信息\n\n @return 统计信息Map\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入问题\n\n @param questions 问题列表\n @return 插入数量\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Long"], "doc": " 批量更新问题状态\n\n @param ids      问题ID列表\n @param status   新状态\n @param updateBy 更新人\n @return 更新数量\n"}], "constructors": []}