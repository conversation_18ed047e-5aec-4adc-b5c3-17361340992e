{"doc": " RAG知识库服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildKnowledgeBaseQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 构建知识库查询条件\n"}, {"name": "buildKnowledgeDocumentQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 构建文档查询条件\n"}, {"name": "processDocumentAsync", "paramTypes": ["java.lang.Long"], "doc": " 异步处理文档\n"}, {"name": "chunkDocument", "paramTypes": ["java.lang.String"], "doc": " 文档分块\n"}, {"name": "updateKnowledgeBaseVectorCount", "paramTypes": ["java.lang.Long"], "doc": " 更新知识库向量数量\n"}, {"name": "updateKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": " 更新知识库统计信息\n"}, {"name": "vectorArrayToString", "paramTypes": ["float[]"], "doc": " 向量数组转换为字符串\n"}], "constructors": []}