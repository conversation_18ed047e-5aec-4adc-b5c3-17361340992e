{"doc": " 工具调用服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.Long"], "doc": " 获取可用的工具列表\n\n @param userId 用户ID\n @return 工具列表\n"}, {"name": "getToolById", "paramTypes": ["java.lang.String"], "doc": " 根据ID获取工具\n\n @param toolId 工具ID\n @return 工具信息\n"}, {"name": "getToolByName", "paramTypes": ["java.lang.String"], "doc": " 根据名称获取工具\n\n @param toolName 工具名称\n @return 工具信息\n"}, {"name": "executeToolCall", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 执行工具调用\n\n @param toolId     工具ID\n @param parameters 调用参数\n @param userId     用户ID\n @param sessionId  会话ID\n @param messageId  消息ID\n @return 调用结果\n"}, {"name": "executeToolCallAsync", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 异步执行工具调用\n\n @param toolId     工具ID\n @param parameters 调用参数\n @param userId     用户ID\n @param sessionId  会话ID\n @param messageId  消息ID\n @return 调用记录ID\n"}, {"name": "getToolCallRecord", "paramTypes": ["java.lang.String"], "doc": " 获取工具调用记录\n\n @param callId 调用记录ID\n @return 调用记录\n"}, {"name": "getUserToolCallHistory", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取用户的工具调用历史\n\n @param userId   用户ID\n @param pageNum  页码\n @param pageSize 每页大小\n @return 调用历史\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String"], "doc": " 获取会话的工具调用记录\n\n @param sessionId 会话ID\n @return 调用记录列表\n"}, {"name": "hasToolPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 验证工具调用权限\n\n @param toolId 工具ID\n @param userId 用户ID\n @return 是否有权限\n"}, {"name": "validateParameters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 验证工具参数\n\n @param toolId     工具ID\n @param parameters 参数\n @return 验证结果\n"}, {"name": "getToolFunctionDefinitions", "paramTypes": ["java.util.List"], "doc": " 获取工具的函数定义（用于AI模型）\n\n @param toolIds 工具ID列表\n @return 函数定义列表\n"}, {"name": "processAiToolCalls", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 解析AI模型的工具调用请求\n\n @param toolCalls AI模型返回的工具调用信息\n @param userId    用户ID\n @param sessionId 会话ID\n @param messageId 消息ID\n @return 工具调用结果列表\n"}], "constructors": []}