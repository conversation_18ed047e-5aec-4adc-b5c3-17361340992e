{"doc": " AI评估服务接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "evaluateAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": " 评估面试回答\n\n @param question    问题内容\n @param answer      回答内容\n @param audioUrl    音频URL（可选）\n @param videoUrl    视频URL（可选）\n @param duration    回答时长\n @param jobContext  岗位上下文信息\n @return 评估结果\n"}, {"name": "generateQuestions", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String", "java.util.List"], "doc": " 生成面试问题\n\n @param jobId       岗位ID\n @param difficulty  难度等级\n @param count       问题数量\n @param resumeUrl   简历URL（可选）\n @param customQuestions 自定义问题（可选）\n @return 生成的问题列表\n"}, {"name": "generateInterviewReport", "paramTypes": ["java.lang.String"], "doc": " 生成面试总结报告\n\n @param sessionId 会话ID\n @return 面试结果\n"}, {"name": "analyzeResume", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 分析简历内容\n\n @param resumeUrl 简历URL\n @param jobId     岗位ID\n @return 简历分析结果\n"}, {"name": "checkAnswerQuality", "paramTypes": ["java.lang.String"], "doc": " 检查回答质量\n\n @param answer 回答内容\n @return 质量评分（0-100）\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.lang.String"], "doc": " 生成改进建议\n\n @param sessionId 会话ID\n @return 改进建议列表\n"}], "constructors": []}