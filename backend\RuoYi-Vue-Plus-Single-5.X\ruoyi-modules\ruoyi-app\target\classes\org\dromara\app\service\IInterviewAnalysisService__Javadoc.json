{"doc": " 面试分析服务接口\n \n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "analyzeEmotion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 分析表情情绪\n \n @param imageData base64编码的图像数据\n @param questionId 问题ID\n @return 情绪分析结果\n"}, {"name": "analyzeSpeech", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 分析语音内容\n \n @param audioData base64编码的音频数据\n @param questionId 问题ID\n @return 语音分析结果\n"}, {"name": "generateEmotionSuggestion", "paramTypes": ["cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": " 基于情绪分析生成智能建议\n \n @param emotionResult 情绪分析结果\n @param questionId 问题ID\n @return 智能建议\n"}, {"name": "generateSpeechSuggestion", "paramTypes": ["cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": " 基于语音分析生成智能建议\n \n @param speechResult 语音分析结果\n @param questionId 问题ID\n @return 智能建议\n"}, {"name": "generateComprehensiveSuggestion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 生成综合面试建议\n \n @param context 面试上下文\n @return 综合建议\n"}], "constructors": []}