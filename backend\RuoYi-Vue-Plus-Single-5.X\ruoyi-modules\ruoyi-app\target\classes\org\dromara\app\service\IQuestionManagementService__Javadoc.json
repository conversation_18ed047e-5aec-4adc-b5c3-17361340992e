{"doc": " 问题管理增强Service接口\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "recommendQuestions", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Bo<PERSON>an"], "doc": " 智能推荐面试问题\n\n @param jobId           岗位ID\n @param technicalDomain 技术领域\n @param difficulty      目标难度\n @param questionCount   问题数量\n @param includeMultimodal 是否包含多模态问题\n @return 推荐问题列表\n"}, {"name": "recommendQuestionsForUser", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": " 根据用户技能水平推荐问题\n\n @param userId        用户ID\n @param jobId         岗位ID\n @param questionCount 问题数量\n @return 个性化推荐问题\n"}, {"name": "getDifficultyGradingSystem", "paramTypes": ["java.lang.String"], "doc": " 获取问题难度分级系统\n\n @param technicalDomain 技术领域\n @return 难度分级信息\n"}, {"name": "analyzeQuestionCoverage", "paramTypes": ["java.lang.Long"], "doc": " 分析问题覆盖度\n\n @param jobId 岗位ID\n @return 覆盖度分析结果\n"}, {"name": "getMultimodalConfig", "paramTypes": ["java.lang.Long"], "doc": " 获取多模态评估配置\n\n @param questionId 问题ID\n @return 多模态评估配置\n"}, {"name": "batchUpdateQuestionTags", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 批量更新问题标签\n\n @param questionIds 问题ID列表\n @param tags        标签列表\n @return 更新结果\n"}], "constructors": []}