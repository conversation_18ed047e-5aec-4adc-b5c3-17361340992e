{"doc": " 题目评论管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目评论列表\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题目评论列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目评论详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目评论\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 审核题目评论\n"}, {"name": "toggleTop", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 置顶/取消置顶题目评论\n"}, {"name": "batchDelete", "paramTypes": ["java.util.List"], "doc": " 批量删除题目评论\n"}, {"name": "getStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取题目评论统计信息\n"}, {"name": "search", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索题目评论\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取题目的评论列表（包含回复）\n"}, {"name": "getReplies", "paramTypes": ["java.lang.Long"], "doc": " 获取评论的回复列表\n"}], "constructors": []}