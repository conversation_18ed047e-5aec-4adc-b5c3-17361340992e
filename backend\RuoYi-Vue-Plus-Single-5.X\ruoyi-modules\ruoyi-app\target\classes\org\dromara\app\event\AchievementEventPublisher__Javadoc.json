{"doc": " 成就事件发布器\n 提供便捷的方法来发布各种成就相关事件\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "publishUserRegistrationEvent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 发布用户注册事件\n\n @param userId 用户ID\n @param source 注册来源\n"}, {"name": "publishLearningCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "int", "int"], "doc": " 发布学习完成事件\n\n @param userId       用户ID\n @param resourceId   资源ID\n @param resourceType 资源类型\n @param duration     学习时长（分钟）\n @param score        得分\n"}, {"name": "publishInterviewCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Long", "int", "int", "java.lang.String"], "doc": " 发布面试完成事件\n\n @param userId      用户ID\n @param interviewId 面试ID\n @param jobId       职位ID\n @param score       面试得分\n @param duration    面试时长（分钟）\n @param mode        面试模式\n"}, {"name": "publishAbilityImproveEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int"], "doc": " 发布能力提升事件\n\n @param userId      用户ID\n @param abilityType 能力类型\n @param oldScore    原分数\n @param newScore    新分数\n"}, {"name": "publishVideoWatchedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int"], "doc": " 发布视频观看完成事件\n\n @param userId   用户ID\n @param videoId  视频ID\n @param duration 观看时长（秒）\n @param progress 观看进度（百分比）\n"}, {"name": "publishArticleReadEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": " 发布文章阅读完成事件\n\n @param userId    用户ID\n @param articleId 文章ID\n @param readTime  阅读时长（分钟）\n"}, {"name": "publishQuestionBankPracticeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int", "int"], "doc": " 发布题库练习完成事件\n\n @param userId       用户ID\n @param questionBankId 题库ID\n @param correctCount 正确题数\n @param totalCount   总题数\n @param duration     练习时长（分钟）\n"}, {"name": "publishResumeImproveEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": " 发布简历完善事件\n\n @param userId       用户ID\n @param resumeId     简历ID\n @param completeness 完整度（百分比）\n"}, {"name": "publishConsecutiveLoginEvent", "paramTypes": ["java.lang.String", "int"], "doc": " 发布连续登录事件\n\n @param userId      用户ID\n @param consecutiveDays 连续登录天数\n"}, {"name": "publishShareEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布分享事件\n\n @param userId   用户ID\n @param shareType 分享类型\n @param targetId  分享目标ID\n @param platform  分享平台\n"}, {"name": "publishCommentEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "int"], "doc": " 发布评论事件\n\n @param userId    用户ID\n @param targetType 评论目标类型\n @param targetId   评论目标ID\n @param commentLength 评论长度\n"}, {"name": "publishFavoriteEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布收藏事件\n\n @param userId     用户ID\n @param targetType 收藏目标类型\n @param targetId   收藏目标ID\n"}, {"name": "publishLikeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布点赞事件\n\n @param userId     用户ID\n @param targetType 点赞目标类型\n @param targetId   点赞目标ID\n"}, {"name": "publishFollowEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布关注事件\n\n @param userId     用户ID\n @param targetType 关注目标类型\n @param targetId   关注目标ID\n"}, {"name": "publishPaymentCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 发布支付完成事件\n\n @param userId  用户ID\n @param orderId 订单ID\n @param amount  支付金额（分）\n @param productType 产品类型\n"}, {"name": "publishInviteFriendEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布邀请好友事件\n\n @param userId     邀请人用户ID\n @param inviteeId  被邀请人用户ID\n @param inviteType 邀请类型\n"}, {"name": "publishProfileUpdateEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": " 发布完善个人信息事件\n\n @param userId       用户ID\n @param profileType  信息类型\n @param completeness 完整度（百分比）\n"}, {"name": "publishSimpleUserLoginEvent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 发布简单的用户登录事件\n\n @param userId 用户ID\n @param source 登录来源\n"}, {"name": "publishSimpleVideoWatchEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 发布简单的视频观看事件\n\n @param userId        用户ID\n @param videoId       视频ID\n @param videoTitle    视频标题\n @param watchDuration 观看时长（秒）\n"}, {"name": "publishSimpleCommentEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布简单的评论事件\n\n @param userId         用户ID\n @param targetType     目标类型（video, article等）\n @param targetId       目标ID\n @param commentContent 评论内容\n"}, {"name": "publishSimpleLikeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发布简单的点赞事件\n\n @param userId     用户ID\n @param targetType 目标类型（video, article, comment等）\n @param targetId   目标ID\n"}, {"name": "publishSimpleStudyTimeEvent", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 发布简单的学习时长事件\n\n @param userId       用户ID\n @param studyMinutes 学习时长（分钟）\n"}], "constructors": []}