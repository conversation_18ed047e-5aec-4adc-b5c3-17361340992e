{"doc": " 视频课程Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryVideoList", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo"], "doc": " 查询视频课程列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 视频列表\n"}, {"name": "getVideoDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取视频详情\n\n @param videoId 视频ID\n @param userId  用户ID\n @return 视频详情\n"}, {"name": "getLearningStats", "paramTypes": ["java.lang.Long"], "doc": " 获取学习统计数据\n\n @param userId 用户ID\n @return 学习统计\n"}, {"name": "getBookmarkedVideos", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取收藏的视频\n\n @param userId    用户ID\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 收藏视频列表\n"}, {"name": "getPurchasedVideos", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取已购买的视频\n\n @param userId    用户ID\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 已购买视频列表\n"}, {"name": "getLearningHistory", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取学习历史\n\n @param userId    用户ID\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 学习历史列表\n"}, {"name": "getRelatedVideos", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取相关推荐视频\n\n @param videoId 视频ID\n @param limit   限制数量\n @return 相关推荐视频列表\n"}, {"name": "toggleVideoLike", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换视频点赞状态\n\n @param videoId 视频ID\n @param userId  用户ID\n @param isLike  是否点赞\n"}, {"name": "toggleVideoCollect", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换视频收藏状态\n\n @param videoId   视频ID\n @param userId    用户ID\n @param isCollect 是否收藏\n"}, {"name": "shareVideo", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": " 分享视频\n\n @param videoId  视频ID\n @param userId   用户ID\n @param platform 分享平台\n"}, {"name": "incrementVideoView", "paramTypes": ["java.lang.Long"], "doc": " 增加视频播放次数\n\n @param videoId 视频ID\n"}, {"name": "updateVideoProgress", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 更新视频播放进度\n\n @param videoId     视频ID\n @param userId      用户ID\n @param currentTime 当前播放时间\n @param duration    视频总时长\n"}, {"name": "getVideoPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取视频播放记录\n\n @param videoId 视频ID\n @param userId  用户ID\n @return 播放记录\n"}, {"name": "checkVideoPurchaseStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 检查视频购买状态\n\n @param videoId 视频ID\n @param userId  用户ID\n @return 购买状态\n"}, {"name": "toggleInstructorFollow", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 关注/取关讲师\n\n @param instructorId 讲师ID\n @param userId       用户ID\n @param isFollow     是否关注\n"}, {"name": "getVideoComments", "paramTypes": ["org.dromara.app.domain.bo.CommentQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取视频评论列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 评论列表\n"}, {"name": "publishVideoComment", "paramTypes": ["org.dromara.app.domain.bo.VideoCommentBo"], "doc": " 发布视频评论\n\n @param bo 评论信息\n @return 评论详情\n"}, {"name": "toggleCommentLike", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 切换评论点赞状态\n\n @param commentId 评论ID\n @param userId    用户ID\n @param isLike    是否点赞\n"}, {"name": "saveVideoPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 保存视频播放记录\n\n @param videoId 视频ID\n @param userId  用户ID\n"}, {"name": "getHotVideos", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门视频列表\n\n @param limit 限制数量\n @return 热门视频列表\n"}], "constructors": []}