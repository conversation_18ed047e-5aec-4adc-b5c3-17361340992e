{"doc": " 用户成就数据层\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "countUnlockedAchievements", "paramTypes": ["java.lang.String"], "doc": " 获取用户已解锁成就数\n\n @param userId 用户ID\n @return 已解锁成就数\n"}, {"name": "countUnlockedAchievementsByCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户某类别已解锁成就数\n\n @param userId   用户ID\n @param category 类别\n @return 该类别已解锁成就数\n"}, {"name": "countUnlockedAchievementsByRarity", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户某稀有度已解锁成就数\n\n @param userId 用户ID\n @param rarity 稀有度\n @return 该稀有度已解锁成就数\n"}, {"name": "countUnlockedAchievementsByDate", "paramTypes": ["java.lang.String", "java.time.LocalDateTime"], "doc": " 获取用户某日期解锁的成就数\n\n @param userId 用户ID\n @param date   日期\n @return 该日期解锁的成就数\n"}, {"name": "sumEarnedPoints", "paramTypes": ["java.lang.String"], "doc": " 获取用户获得的成就点数总和\n\n @param userId 用户ID\n @return 成就点数总和\n"}, {"name": "selectRecentAchievements", "paramTypes": ["java.lang.String", "int"], "doc": " 获取用户最近解锁的成就\n\n @param userId 用户ID\n @param limit  数量限制\n @return 最近解锁的成就\n"}, {"name": "selectInProgressAchievements", "paramTypes": ["java.lang.String"], "doc": " 获取用户进行中的成就\n\n @param userId 用户ID\n @return 进行中的成就\n"}, {"name": "selectUserAchievementDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户成就详情\n\n @param userId        用户ID\n @param achievementId 成就ID\n @return 用户成就详情\n"}, {"name": "selectRecommendedAchievements", "paramTypes": ["java.lang.String", "int"], "doc": " 获取用户推荐的成就\n\n @param userId 用户ID\n @param limit  数量限制\n @return 推荐的成就\n"}, {"name": "selectLeaderboard", "paramTypes": ["java.lang.String", "int"], "doc": " 获取成就排行榜\n\n @param category 类别（可选）\n @param limit    数量限制\n @return 排行榜列表\n"}, {"name": "getUserRanking", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户排名\n\n @param userId   用户ID\n @param category 类别（可选）\n @return 用户排名\n"}, {"name": "selectByUserIdAndAchievementId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据用户ID和成就ID获取用户成就\n\n @param userId        用户ID\n @param achievementId 成就ID\n @return 用户成就\n"}, {"name": "sumEventValues", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取用户某事件类型的累计事件值\n\n @param userId    用户ID\n @param eventType 事件类型\n @return 累计事件值\n"}, {"name": "updateProgress", "paramTypes": ["org.dromara.app.domain.UserAchievement"], "doc": " 更新用户成就进度\n\n @param userAchievement 用户成就\n @return 更新行数\n"}, {"name": "selectUnlockedCandidates", "paramTypes": ["java.lang.String"], "doc": " 查询未解锁且符合条件的成就\n\n @param userId 用户ID\n @return 符合条件的成就列表\n"}, {"name": "updateNotificationStatus", "paramTypes": ["java.lang.String", "int"], "doc": " 更新通知状态\n\n @param userId 用户ID\n @param status 状态（0=未通知，1=已通知）\n @return 更新行数\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 标记成就为已查看\n\n @param userId        用户ID\n @param achievementId 成就ID\n @return 更新行数\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询用户成就列表\n\n @param userId 用户ID\n @return 用户成就列表\n"}, {"name": "selectCompletedByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户已完成的成就列表\n\n @param userId 用户ID\n @return 已完成的成就列表\n"}, {"name": "selectInProgressByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户进行中的成就列表\n\n @param userId 用户ID\n @return 进行中的成就列表\n"}, {"name": "countCompletedByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户已完成的成就数量\n\n @param userId 用户ID\n @return 已完成的成就数量\n"}, {"name": "sumRewardPointsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户获得的总积分\n\n @param userId 用户ID\n @return 总积分\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Long", "java.math.BigDecimal"], "doc": " 更新用户成就进度\n\n @param userId        用户ID\n @param achievementId 成就ID\n @param currentValue  当前值\n @param progress      进度\n @return 更新行数\n"}, {"name": "completeAchievement", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 完成用户成就\n\n @param userId        用户ID\n @param achievementId 成就ID\n @return 更新行数\n"}, {"name": "selectStatsByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据成就类型查询用户成就统计\n\n @param userId          用户ID\n @param achievementType 成就类型\n @return 用户成就统计列表\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近的成就列表\n\n @param aLong 用户ID\n @param limit  数量限制\n @return 最近的成就列表\n"}], "constructors": []}