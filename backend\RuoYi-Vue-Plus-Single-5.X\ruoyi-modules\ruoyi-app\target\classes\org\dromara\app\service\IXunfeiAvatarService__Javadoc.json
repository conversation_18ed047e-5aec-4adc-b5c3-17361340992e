{"doc": " 讯飞数字人服务接口\n\n <AUTHOR>\n @date 2025-07-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "startSession", "paramTypes": ["org.dromara.app.domain.dto.avatar.AvatarStartDto"], "doc": " 启动数字人会话\n\n @param startDto 启动参数\n @return 会话信息\n"}, {"name": "sendTextDriver", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": " 发送文本驱动\n\n @param sessionId 会话ID\n @param textDto   文本参数\n @return 是否发送成功\n"}, {"name": "sendTextInteract", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": " 发送文本交互\n\n @param sessionId 会话ID\n @param textDto   文本参数\n @return 是否发送成功\n"}, {"name": "sendAudioDriver", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": " 发送音频驱动\n\n @param sessionId  会话ID\n @param audioData  音频数据（Base64编码）\n @param status     数据状态（0开始，1过渡，2结束）\n @return 是否发送成功\n"}, {"name": "resetAvatar", "paramTypes": ["java.lang.String"], "doc": " 重置（打断）数字人\n\n @param sessionId 会话ID\n @return 是否重置成功\n"}, {"name": "stopSession", "paramTypes": ["java.lang.String"], "doc": " 停止数字人会话\n\n @param sessionId 会话ID\n @return 是否停止成功\n"}, {"name": "sendHeartbeat", "paramTypes": ["java.lang.String"], "doc": " 发送心跳\n\n @param sessionId 会话ID\n @return 是否发送成功\n"}, {"name": "sendAction", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送动作指令\n\n @param sessionId   会话ID\n @param actionType  动作类型\n @param actionValue 动作值\n @return 是否发送成功\n"}, {"name": "setMessageHandler", "paramTypes": ["java.lang.String", "java.util.function.Consumer"], "doc": " 设置消息处理器\n\n @param sessionId      会话ID\n @param messageHandler 消息处理器\n"}, {"name": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.util.function.Consumer"], "doc": " 设置错误处理器\n\n @param sessionId    会话ID\n @param errorHandler 错误处理器\n"}, {"name": "isSessionActive", "paramTypes": ["java.lang.String"], "doc": " 检查会话状态\n\n @param sessionId 会话ID\n @return 是否连接中\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": " 获取会话信息\n\n @param sessionId 会话ID\n @return 会话信息\n"}, {"name": "updateStreamUrl", "paramTypes": ["java.lang.String"], "doc": " 更新会话推流地址\n\n @param sessionId 会话ID\n @return 是否更新成功\n"}], "constructors": []}