{"doc": " 面试历史记录Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID查询历史记录列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 历史记录列表\n"}, {"name": "selectByUserIdAndResultId", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和结果ID查询历史记录\n\n @param userId   用户ID\n @param resultId 结果ID\n @return 历史记录\n"}, {"name": "selectFavoritesByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的历史记录\n\n @param userId 用户ID\n @return 历史记录列表\n"}, {"name": "selectByUserIdAndTags", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 根据标签查询历史记录\n\n @param userId 用户ID\n @param tags   标签列表\n @return 历史记录列表\n"}, {"name": "updateFavoriteStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 更新收藏状态\n\n @param id         历史记录ID\n @param isFavorite 是否收藏\n @return 更新数量\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID删除历史记录\n\n @param userId 用户ID\n @return 删除数量\n"}, {"name": "selectCountByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户历史记录数量\n\n @param userId 用户ID\n @return 记录数量\n"}], "constructors": []}