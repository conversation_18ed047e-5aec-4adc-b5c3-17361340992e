{"doc": " 应用用户Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateSmsCode", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证短信验证码\n\n @param phone   手机号\n @param smsCode 短信验证码\n @return 验证结果\n"}, {"name": "validateEmailCode", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证邮箱验证码\n\n @param email     邮箱\n @param emailCode 邮箱验证码\n @return 验证结果\n"}], "constructors": []}