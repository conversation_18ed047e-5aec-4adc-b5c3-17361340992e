{"doc": " 讯飞数字人协议构建服务\n\n <AUTHOR>\n @date 2025-07-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildStartRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 构建启动协议\n\n @param avatarId   形象ID\n @param sceneId    场景ID\n @param vcn        声音ID\n @param width      视频宽度\n @param height     视频高度\n @param background 背景数据（可选）\n @return 启动协议JSON\n"}, {"name": "buildTextRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 构建文本驱动协议\n\n @param text   文本内容\n @param vcn    声音ID（可选）\n @param speed  语速（可选）\n @param pitch  语调（可选）\n @param volume 音量（可选）\n @return 文本驱动协议JSON\n"}, {"name": "buildTextInteractRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 构建文本交互协议\n\n @param text   文本内容\n @param vcn    声音ID（可选）\n @param speed  语速（可选）\n @param pitch  语调（可选）\n @param volume 音量（可选）\n @return 文本交互协议JSON\n"}, {"name": "buildAudioRequest", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": " 构建音频驱动协议\n\n @param requestId  请求ID\n @param status     数据状态（0开始，1过渡，2结束）\n @param audioData  音频数据（Base64编码）\n @return 音频驱动协议JSON\n"}, {"name": "buildPingRequest", "paramTypes": [], "doc": " 构建心跳协议\n\n @return 心跳协议JSON\n"}, {"name": "buildResetRequest", "paramTypes": [], "doc": " 构建重置（打断）协议\n\n @return 重置协议JSON\n"}, {"name": "buildStopRequest", "paramTypes": [], "doc": " 构建停止协议\n\n @return 停止协议JSON\n"}, {"name": "buildCmdRequest", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建单独指令协议\n\n @param actionType  动作类型\n @param actionValue 动作值\n @return 指令协议JSON\n"}], "constructors": []}