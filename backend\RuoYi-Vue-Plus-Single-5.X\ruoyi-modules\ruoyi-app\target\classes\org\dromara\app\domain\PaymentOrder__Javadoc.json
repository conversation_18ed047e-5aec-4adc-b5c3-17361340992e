{"doc": " 支付订单实体类\n\n <AUTHOR>\n", "fields": [{"name": "orderId", "doc": " 订单ID\n"}, {"name": "orderNo", "doc": " 订单号\n"}, {"name": "productId", "doc": " 商品ID\n"}, {"name": "productType", "doc": " 商品类型\n"}, {"name": "productTitle", "doc": " 商品标题\n"}, {"name": "amount", "doc": " 支付金额\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "paymentMethod", "doc": " 支付方式\n"}, {"name": "status", "doc": " 订单状态：pending-待支付，paid-已支付，cancelled-已取消，expired-已过期\n"}, {"name": "alipayTradeNo", "doc": " 支付宝交易号\n"}, {"name": "payTime", "doc": " 支付时间\n"}, {"name": "expireTime", "doc": " 过期时间\n"}, {"name": "clientIp", "doc": " 客户端IP\n"}, {"name": "userAgent", "doc": " 用户代理\n"}, {"name": "notifyCount", "doc": " 回调通知次数\n"}, {"name": "lastNotifyTime", "doc": " 最后通知时间\n"}, {"name": "notifyR<PERSON>ult", "doc": " 通知结果\n"}, {"name": "remark", "doc": " 备注信息\n"}, {"name": "payToken", "doc": " 支付token\n"}, {"name": "payTokenExpireTime", "doc": " 支付token过期时间\n"}, {"name": "payTokenUsed", "doc": " 支付token是否已使用\n"}], "enumConstants": [], "methods": [], "constructors": []}