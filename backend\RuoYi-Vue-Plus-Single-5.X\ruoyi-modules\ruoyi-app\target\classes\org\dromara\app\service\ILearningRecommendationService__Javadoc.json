{"doc": " 学习推荐服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateLearningPaths", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": " 基于面试报告生成学习路径推荐\n\n @param report 面试报告\n @return 学习路径推荐列表\n"}, {"name": "generatePathsByWeaknesses", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 基于弱项生成学习推荐\n\n @param weaknesses 弱项列表\n @param jobPosition 岗位信息\n @return 学习路径推荐列表\n"}, {"name": "generatePathsByJobPosition", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 基于岗位生成学习推荐\n\n @param jobPosition 岗位信息\n @param userLevel 用户水平\n @return 学习路径推荐列表\n"}, {"name": "generatePathsByDimensionScores", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 基于维度评分生成学习推荐\n\n @param dimensionScores 维度评分列表\n @param jobPosition 岗位信息\n @return 学习路径推荐列表\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 获取学习资源推荐\n\n @param skillArea 技能领域\n @param difficulty 难度等级\n @param resourceType 资源类型\n @return 学习资源列表\n"}, {"name": "calculateLearningPriorities", "paramTypes": ["java.util.List", "java.util.List", "java.lang.String"], "doc": " 计算学习路径优先级\n\n @param weaknesses 弱项列表\n @param dimensionScores 维度评分\n @param jobPosition 岗位信息\n @return 优先级映射\n"}, {"name": "personalizelearningPaths", "paramTypes": ["java.util.List", "org.dromara.app.service.ILearningRecommendationService.UserProfile"], "doc": " 个性化学习路径调整\n\n @param basePaths 基础学习路径\n @param userProfile 用户画像\n @return 调整后的学习路径\n"}, {"name": "getLearningPathDetail", "paramTypes": ["java.lang.String"], "doc": " 获取学习路径详情\n\n @param pathId 路径ID\n @return 学习路径详情\n"}], "constructors": []}