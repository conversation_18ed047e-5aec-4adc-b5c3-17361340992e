{"doc": " 能力评估服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAssessmentQuestions", "paramTypes": [], "doc": " 获取评估问题列表\n\n @return 评估问题列表\n"}, {"name": "submitAssessmentResults", "paramTypes": ["java.util.List"], "doc": " 提交评估结果\n\n @param results 评估结果\n @return 能力评估结果\n"}, {"name": "getDetailedAbilityReport", "paramTypes": [], "doc": " 获取详细能力报告\n\n @return 详细能力报告\n"}, {"name": "getUserGrowthProfile", "paramTypes": [], "doc": " 获取用户成长档案\n\n @return 用户成长档案\n"}], "constructors": []}