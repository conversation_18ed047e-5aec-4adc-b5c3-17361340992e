{"doc": " 用户评估结果Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectResultsByRecordId", "paramTypes": ["java.lang.Long"], "doc": " 根据记录ID查询评估结果列表\n\n @param recordId 记录ID\n @return 评估结果列表\n"}, {"name": "selectResultsByRecordIdAndCategory", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据记录ID和类别查询评估结果列表\n\n @param recordId 记录ID\n @param category 类别\n @return 评估结果列表\n"}, {"name": "selectCategoryAverageScoresByRecordId", "paramTypes": ["java.lang.Long"], "doc": " 根据记录ID查询各类别的平均分\n\n @param recordId 记录ID\n @return 各类别平均分\n"}, {"name": "insertBatch", "paramTypes": ["java.util.List"], "doc": " 批量插入评估结果\n\n @param results 评估结果列表\n @return 插入数量\n"}, {"name": "deleteByRecordId", "paramTypes": ["java.lang.Long"], "doc": " 根据记录ID删除评估结果\n\n @param recordId 记录ID\n @return 删除数量\n"}, {"name": "selectResultsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询所有评估结果\n\n @param userId 用户ID\n @return 评估结果列表\n"}], "constructors": []}