{"doc": " 面试结果服务接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "getResultSummary", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果摘要\n\n @param resultId 结果ID或会话ID\n @return 面试结果摘要\n"}, {"name": "getResultDetail", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果详情\n\n @param resultId 结果ID或会话ID\n @return 面试结果详情\n"}, {"name": "getPerformanceMetrics", "paramTypes": ["java.lang.String"], "doc": " 获取性能指标\n\n @param resultId 结果ID\n @return 性能指标数据\n"}, {"name": "saveToHistory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 保存到历史记录\n\n @param resultId 结果ID\n @param title    自定义标题（可选）\n @return 保存结果\n"}, {"name": "shareResult", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 分享结果\n\n @param resultId 结果ID\n @param platform 分享平台\n @param content  分享内容（可选）\n @return 分享结果\n"}, {"name": "getImprovementPlan", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取提升计划\n\n @param resultId 结果ID\n @param userId   用户ID（可选）\n @return 提升计划\n"}, {"name": "getLearningResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取学习资源推荐\n\n @param resultId 结果ID\n @param limit    数量限制\n @return 学习资源列表\n"}, {"name": "createInterviewResult", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 创建面试结果\n\n @param sessionId 会话ID\n @param resultData 结果数据\n @return 结果ID\n"}, {"name": "updateInterviewResult", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 更新面试结果\n\n @param resultId 结果ID\n @param resultData 结果数据\n @return 是否成功\n"}, {"name": "deleteInterviewResult", "paramTypes": ["java.lang.String"], "doc": " 删除面试结果\n\n @param resultId 结果ID\n @return 是否成功\n"}, {"name": "getUserResults", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取用户面试结果列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 面试结果列表\n"}, {"name": "getUserStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取用户面试统计\n\n @param userId 用户ID\n @return 统计数据\n"}, {"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": " 生成面试报告\n\n @param resultId 结果ID\n @return 报告内容\n"}], "constructors": []}