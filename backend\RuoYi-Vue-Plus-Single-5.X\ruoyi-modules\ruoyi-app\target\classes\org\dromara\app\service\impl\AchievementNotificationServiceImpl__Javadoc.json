{"doc": " 成就通知服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendNotificationToQueue", "paramTypes": ["java.util.Map"], "doc": " 发送通知到消息队列\n"}, {"name": "processNotificationDirectly", "paramTypes": ["java.util.Map"], "doc": " 直接处理通知（不通过MQ）\n"}, {"name": "shouldSendProgressNotification", "paramTypes": ["double"], "doc": " 判断是否应该发送进度通知\n"}, {"name": "getCategoryDisplayName", "paramTypes": ["java.lang.String"], "doc": " 获取类别显示名称\n"}], "constructors": []}