{"doc": " 面试缓存服务接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "cacheSession", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": " 缓存面试会话信息\n\n @param session 会话信息\n"}, {"name": "getCachedSession", "paramTypes": ["java.lang.String"], "doc": " 获取缓存的会话信息\n\n @param sessionId 会话ID\n @return 会话信息\n"}, {"name": "evictSession", "paramTypes": ["java.lang.String"], "doc": " 删除会话缓存\n\n @param sessionId 会话ID\n"}, {"name": "cacheSessionQuestions", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 缓存会话问题列表\n\n @param sessionId 会话ID\n @param questions 问题列表\n"}, {"name": "getCachedSessionQuestions", "paramTypes": ["java.lang.String"], "doc": " 获取缓存的会话问题列表\n\n @param sessionId 会话ID\n @return 问题列表\n"}, {"name": "evictSessionQuestions", "paramTypes": ["java.lang.String"], "doc": " 删除会话问题缓存\n\n @param sessionId 会话ID\n"}, {"name": "cacheUserActiveSession", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 缓存用户当前活跃会话\n\n @param userId    用户ID\n @param sessionId 会话ID\n"}, {"name": "getUserActiveSession", "paramTypes": ["java.lang.Long"], "doc": " 获取用户当前活跃会话\n\n @param userId 用户ID\n @return 会话ID\n"}, {"name": "evictUserActiveSession", "paramTypes": ["java.lang.Long"], "doc": " 删除用户活跃会话缓存\n\n @param userId 用户ID\n"}, {"name": "cacheInterviewResult", "paramTypes": ["java.lang.String", "org.dromara.app.domain.vo.InterviewResponseVo.InterviewResult"], "doc": " 缓存面试结果\n\n @param sessionId 会话ID\n @param result    面试结果\n"}, {"name": "getCachedInterviewResult", "paramTypes": ["java.lang.String"], "doc": " 获取缓存的面试结果\n\n @param sessionId 会话ID\n @return 面试结果\n"}, {"name": "evictInterviewResult", "paramTypes": ["java.lang.String"], "doc": " 删除面试结果缓存\n\n @param sessionId 会话ID\n"}, {"name": "cacheAiEvaluation", "paramTypes": ["java.lang.String", "java.lang.String", "org.dromara.app.domain.vo.InterviewResponseVo.FeedbackInfo"], "doc": " 缓存AI评估结果\n\n @param questionId 问题ID\n @param answer     答案内容\n @param feedback   评估结果\n"}, {"name": "getCachedAiEvaluation", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取缓存的AI评估结果\n\n @param questionId 问题ID\n @param answer     答案内容\n @return 评估结果\n"}, {"name": "cachePopularQuestions", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 缓存热门问题\n\n @param jobId     岗位ID\n @param questions 问题列表\n"}, {"name": "getCachedPopularQuestions", "paramTypes": ["java.lang.Long"], "doc": " 获取缓存的热门问题\n\n @param jobId 岗位ID\n @return 问题列表\n"}, {"name": "cacheUserStats", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InterviewResponseVo.UserStats"], "doc": " 缓存用户统计信息\n\n @param userId 用户ID\n @param stats  统计信息\n"}, {"name": "getCachedUserStats", "paramTypes": ["java.lang.Long"], "doc": " 获取缓存的用户统计信息\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "warmupCache", "paramTypes": ["java.lang.String"], "doc": " 预热缓存\n\n @param sessionId 会话ID\n"}, {"name": "cleanExpiredCache", "paramTypes": [], "doc": " 清理过期缓存\n"}, {"name": "clearAllCache", "paramTypes": [], "doc": " 清理所有缓存\n"}, {"name": "getCacheStats", "paramTypes": [], "doc": " 获取缓存统计信息\n\n @return 缓存统计\n"}], "constructors": []}