{"doc": " 成就系统消息监听器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleAchievementCheck", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": " 处理成就检查消息\n"}, {"name": "handleAchievementNotification", "paramTypes": ["java.lang.String", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": " 处理成就通知消息\n"}, {"name": "handleDeadLetterMessage", "paramTypes": ["java.lang.String", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": " 处理死信队列消息\n"}, {"name": "processAchievementNotification", "paramTypes": ["java.lang.String"], "doc": " 处理成就通知的具体逻辑\n"}], "constructors": []}