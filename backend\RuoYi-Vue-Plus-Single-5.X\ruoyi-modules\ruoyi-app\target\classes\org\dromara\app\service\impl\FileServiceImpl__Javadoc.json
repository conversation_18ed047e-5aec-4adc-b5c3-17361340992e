{"doc": " 文件服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 验证文件\n"}, {"name": "generateFileName", "paramTypes": ["java.lang.String"], "doc": " 生成文件名\n"}, {"name": "generateRelativePath", "paramTypes": ["java.lang.String"], "doc": " 生成相对路径\n"}, {"name": "formatFileSize", "paramTypes": ["long"], "doc": " 格式化文件大小\n"}], "constructors": []}