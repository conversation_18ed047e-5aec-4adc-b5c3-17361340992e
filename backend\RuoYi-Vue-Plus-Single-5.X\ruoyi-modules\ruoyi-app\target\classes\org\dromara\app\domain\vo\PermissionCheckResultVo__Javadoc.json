{"doc": " 权限检查结果视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "checkId", "doc": " 检查ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "resource", "doc": " 资源标识\n"}, {"name": "action", "doc": " 操作类型\n"}, {"name": "hasPermission", "doc": " 是否有权限\n"}, {"name": "result", "doc": " 检查结果：granted/denied/expired/suspended\n"}, {"name": "denyReason", "doc": " 拒绝原因\n"}, {"name": "permissionSource", "doc": " 权限来源：role/user/group\n"}, {"name": "roles", "doc": " 相关角色列表\n"}, {"name": "permissionExpireTime", "doc": " 权限过期时间\n"}, {"name": "checkDuration", "doc": " 检查耗时（毫秒）\n"}, {"name": "checkTime", "doc": " 检查时间\n"}, {"name": "clientIp", "doc": " 客户端IP\n"}, {"name": "userAgent", "doc": " 用户代理\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "needsAdditionalAuth", "doc": " 是否需要额外验证\n"}, {"name": "additionalAuthType", "doc": " 额外验证类型\n"}, {"name": "riskScore", "doc": " 风险评分（0-100）\n"}, {"name": "riskFactors", "doc": " 风险因素\n"}, {"name": "rateLimitStatus", "doc": " 访问频率限制状态\n"}, {"name": "remainingAccess", "doc": " 剩余访问次数\n"}, {"name": "rateLimitResetTime", "doc": " 限制重置时间\n"}, {"name": "auditInfo", "doc": " 审计信息\n"}, {"name": "logRecorded", "doc": " 是否记录日志\n"}], "enumConstants": [], "methods": [], "constructors": []}