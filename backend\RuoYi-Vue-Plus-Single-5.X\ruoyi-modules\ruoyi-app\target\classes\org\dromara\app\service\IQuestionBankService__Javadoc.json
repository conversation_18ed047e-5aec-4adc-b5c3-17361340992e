{"doc": " 题库管理Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库\n\n @param bankId 题库主键\n @return 题库\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题库列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 题库分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 查询题库列表\n\n @param bo 查询条件\n @return 题库列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 新增题库\n\n @param bo 题库信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 修改题库\n\n @param bo 题库信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除题库信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 删除结果\n"}, {"name": "importBank", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 批量导入题库\n\n @param bankList 题库列表\n @param isUpdateSupport 是否支持更新\n @param operName 操作人员\n @return 导入结果信息\n"}, {"name": "exportBankList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 导出题库列表\n\n @param bo 查询条件\n @return 题库列表\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题库状态\n\n @param bankId 题库ID\n @param status 状态\n @return 更新结果\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题库状态\n\n @param bankIds 题库ID集合\n @param status  状态\n @return 更新结果\n"}, {"name": "copyBank", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 复制题库\n\n @param bankId   源题库ID\n @param bankCode 新题库编码\n @param title    新题库标题\n @return 复制结果\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n\n @param bankId 题库ID\n @return 更新结果\n"}, {"name": "getBankStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n\n @param bankId 题库ID\n @return 统计信息\n"}, {"name": "batchSetSort", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 批量设置题库排序\n\n @param bankIds 题库ID列表\n @param sorts   排序值列表\n @return 设置结果\n"}], "constructors": []}