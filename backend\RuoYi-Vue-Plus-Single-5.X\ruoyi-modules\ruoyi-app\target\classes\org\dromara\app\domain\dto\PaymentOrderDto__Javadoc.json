{"doc": " 支付订单请求DTO\n\n <AUTHOR>\n", "fields": [{"name": "productId", "doc": " 商品ID\n"}, {"name": "productType", "doc": " 商品类型\n"}, {"name": "productTitle", "doc": " 商品标题\n"}, {"name": "amount", "doc": " 支付金额\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "paymentMethod", "doc": " 支付方式\n"}, {"name": "clientIp", "doc": " 客户端IP\n"}, {"name": "userAgent", "doc": " 用户代理\n"}, {"name": "remark", "doc": " 备注信息\n"}], "enumConstants": [], "methods": [], "constructors": []}