{"doc": " 成就定义Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByAchievementCode", "paramTypes": ["java.lang.String"], "doc": " 根据成就代码查询成就\n\n @param achievementCode 成就代码\n @return 成就信息\n"}, {"name": "selectByAchievementType", "paramTypes": ["java.lang.String"], "doc": " 根据成就类型查询成就列表\n\n @param achievementType 成就类型\n @return 成就列表\n"}, {"name": "selectActiveAchievements", "paramTypes": [], "doc": " 查询激活的成就列表\n\n @return 激活的成就列表\n"}, {"name": "selectOrderBySortOrder", "paramTypes": [], "doc": " 根据排序查询成就列表\n\n @return 按排序的成就列表\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新成就状态\n\n @param ids      成就ID列表\n @param isActive 是否激活\n @return 更新数量\n"}, {"name": "countTotalAchievements", "paramTypes": [], "doc": " 获取总成就数\n\n @return 总成就数\n"}, {"name": "countAchievementsByType", "paramTypes": ["java.lang.String"], "doc": " 根据类型获取成就数\n\n @param achievementType 成就类型\n @return 该类型的成就数\n"}, {"name": "sumTotalRewardPoints", "paramTypes": [], "doc": " 获取成就总积分\n\n @return 成就总积分\n"}, {"name": "countAchievementsByRarity", "paramTypes": ["java.lang.String"], "doc": " 根据稀有度获取成就数\n @param rarity 稀有度\n @return 该稀有度的成就数\n"}], "constructors": []}