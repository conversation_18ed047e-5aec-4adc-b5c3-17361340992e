{"doc": " 学习资源Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getMajorList", "paramTypes": [], "doc": " 获取专业列表\n\n @return 专业列表\n"}, {"name": "getQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": " 获取题库列表\n\n @param queryDto 查询参数\n @return 题库列表\n"}, {"name": "toggleBookmark", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 切换题库收藏状态\n\n @param userId       用户ID\n @param bankId       题库ID\n @param isBookmarked 是否收藏\n @return 收藏状态\n"}, {"name": "searchQuestionBanks", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 搜索题库\n\n @param keyword 关键词\n @param majorId 专业ID\n @return 题库列表\n"}, {"name": "getQuestionBankDetail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 获取题库详情\n\n @param bankId  题库ID\n @param majorId 专业ID\n @param userId  用户ID\n @return 题库详情\n"}, {"name": "getHotQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门题库\n\n @param limit 限制数量\n @return 题库列表\n"}, {"name": "getNewQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": " 获取最新题库\n\n @param limit 限制数量\n @return 题库列表\n"}, {"name": "getQuestionBankFullDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取题库详细信息\n\n @param bankId 题库ID\n @param userId 用户ID\n @return 题库详细信息\n"}, {"name": "getQuestionsByCategory", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取题库分类题目\n\n @param bankId 题库ID\n @return 按分类组织的题目列表\n"}, {"name": "getRecommendedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取推荐题目\n\n @param bankId 题库ID\n @param limit  限制数量\n @return 推荐题目列表\n"}, {"name": "toggleQuestionBankBookmark", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 切换题库收藏状态（新版）\n\n @param userId 用户ID\n @param bankId 题库ID\n @return 收藏结果\n"}, {"name": "getQuestionList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionQueryDto"], "doc": " 获取题库题目列表（支持筛选和搜索）\n\n @param bankId   题库ID\n @param queryDto 查询参数\n @return 题目列表\n"}, {"name": "getQuestionDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取题目详情\n\n @param questionId 题目ID\n @param userId     用户ID\n @return 题目详情\n"}, {"name": "searchQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.Long"], "doc": " 搜索题目\n\n @param bankId     题库ID\n @param keyword    搜索关键词\n @param difficulty 难度等级\n @param category   分类\n @param completed  完成状态\n @param userId     用户ID\n @return 题目列表\n"}, {"name": "toggleQuestionBookmark", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 切换题目收藏状态\n\n @param userId       用户ID\n @param questionId   题目ID\n @param isBookmarked 是否收藏\n @return 收藏状态结果\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取题目评论列表\n\n @param questionId     题目ID\n @param page           页码\n @param pageSize       每页大小\n @param orderBy        排序字段\n @param orderDirection 排序方向\n @return 评论列表\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 创建题目评论\n\n @param userId     用户ID\n @param questionId 题目ID\n @param content    评论内容\n @param parentId   父评论ID\n @return 创建的评论\n"}, {"name": "deleteQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 删除题目评论\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 删除结果\n"}, {"name": "likeQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 点赞/取消点赞评论\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 点赞结果\n"}, {"name": "submitPracticeRecord", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 提交题目练习记录\n\n @param userId     用户ID\n @param questionId 题目ID\n @param userAnswer 用户答案\n @param timeSpent  用时（秒）\n @return 提交结果\n"}, {"name": "getQuestionStats", "paramTypes": ["java.lang.String"], "doc": " 获取题目统计信息\n\n @param questionId 题目ID\n @return 统计信息\n"}, {"name": "getRelatedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取相关题目推荐\n\n @param questionId 题目ID\n @param limit      推荐数量限制\n @return 相关题目列表\n"}, {"name": "reportContent", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 举报题目或评论\n\n @param userId      用户ID\n @param targetId    目标ID\n @param targetType  目标类型（question/comment）\n @param reason      举报原因\n @param description 详细描述\n @return 举报结果\n"}, {"name": "getLearningStats", "paramTypes": ["java.lang.Long"], "doc": " 获取学习统计数据\n\n @param userId 用户ID\n @return 学习统计数据\n"}, {"name": "getTodayRecommendation", "paramTypes": ["java.lang.Long"], "doc": " 获取今日推荐内容\n\n @param userId 用户ID\n @return 今日推荐内容\n"}, {"name": "getResourceCategoryStats", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取资源分类统计数据\n\n @param majorId 专业ID（可选）\n @param userId  用户ID（可选）\n @return 资源分类统计数据\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取题库题目统计信息\n\n @param bankId 题库ID\n @return 统计信息\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": " 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\n\n @param queryDto 查询参数\n @param userId   用户ID\n @return 题库列表\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取专业题库统计信息\n\n @param majorId 专业ID\n @param userId  用户ID\n @return 统计信息\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取专业题库筛选选项计数\n\n @param majorId 专业ID\n @param userId  用户ID\n @return 筛选选项计数\n"}], "constructors": []}