{"doc": " 缓存优化服务接口\n 用于优化系统缓存策略和性能\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "warmupCache", "paramTypes": [], "doc": " 预热常用数据缓存\n"}, {"name": "cleanExpiredCache", "paramTypes": [], "doc": " 清理过期缓存\n\n @return 清理的缓存数量\n"}, {"name": "getCacheStatistics", "paramTypes": [], "doc": " 获取缓存统计信息\n\n @return 缓存统计\n"}, {"name": "refreshCache", "paramTypes": ["java.lang.String"], "doc": " 刷新指定缓存\n\n @param cacheKey 缓存键\n @return 是否刷新成功\n"}, {"name": "batchRefreshCache", "paramTypes": ["java.util.List"], "doc": " 批量刷新缓存\n\n @param cacheKeys 缓存键列表\n @return 刷新成功的数量\n"}, {"name": "getHotData", "paramTypes": ["int"], "doc": " 获取热点数据\n\n @param limit 限制数量\n @return 热点数据键值对\n"}, {"name": "setCacheWarmupStrategy", "paramTypes": ["java.lang.String"], "doc": " 设置缓存预热策略\n\n @param strategy 预热策略\n"}, {"name": "optimizeCacheConfiguration", "paramTypes": [], "doc": " 优化缓存配置\n"}, {"name": "monitorCachePerformance", "paramTypes": [], "doc": " 监控缓存性能\n\n @return 性能指标\n"}, {"name": "analyzeCacheHitRate", "paramTypes": [], "doc": " 分析缓存命中率\n\n @return 命中率分析结果\n"}, {"name": "cleanLowFrequencyCache", "paramTypes": ["int"], "doc": " 清理低频访问缓存\n\n @param threshold 访问频率阈值\n @return 清理的缓存数量\n"}, {"name": "compressCacheData", "paramTypes": [], "doc": " 压缩缓存数据\n\n @return 压缩节省的空间大小（字节）\n"}], "constructors": []}