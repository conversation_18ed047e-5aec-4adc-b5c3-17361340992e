{"doc": " 知识库文档业务对象 app_knowledge_document\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 文档ID\n"}, {"name": "knowledgeBaseId", "doc": " 知识库ID\n"}, {"name": "title", "doc": " 文档标题\n"}, {"name": "content", "doc": " 文档内容\n"}, {"name": "docType", "doc": " 文档类型 (text/pdf/word/markdown/etc.)\n"}, {"name": "source", "doc": " 文档来源 (upload/url/api/etc.)\n"}, {"name": "originalFilename", "doc": " 原始文件名\n"}, {"name": "filePath", "doc": " 文件路径\n"}, {"name": "fileSize", "doc": " 文件大小 (字节)\n"}, {"name": "status", "doc": " 文档状态 (0=处理中 1=已完成 2=失败)\n"}, {"name": "processStatus", "doc": " 处理状态 (0=未处理 1=已向量化 2=已索引)\n"}, {"name": "summary", "doc": " 文档摘要\n"}, {"name": "tags", "doc": " 文档标签 (JSON数组)\n"}, {"name": "metadata", "doc": " 文档元数据 (JSON格式)\n"}, {"name": "processConfig", "doc": " 处理配置 (JSON格式)\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "keyword", "doc": " 搜索关键词（用于标题和内容的模糊查询）\n"}, {"name": "docTypes", "doc": " 文档类型列表（用于多选过滤）\n"}, {"name": "sources", "doc": " 文档来源列表（用于多选过滤）\n"}, {"name": "statuses", "doc": " 状态列表（用于多选过滤）\n"}, {"name": "processStatuses", "doc": " 处理状态列表（用于多选过滤）\n"}, {"name": "createTimeStart", "doc": " 创建时间范围 - 开始时间\n"}, {"name": "createTimeEnd", "doc": " 创建时间范围 - 结束时间\n"}, {"name": "fileSizeMin", "doc": " 文件大小范围 - 最小值\n"}, {"name": "fileSizeMax", "doc": " 文件大小范围 - 最大值\n"}, {"name": "vectorCountMin", "doc": " 向量数量范围 - 最小值\n"}, {"name": "vectorCountMax", "doc": " 向量数量范围 - 最大值\n"}], "enumConstants": [], "methods": [], "constructors": []}