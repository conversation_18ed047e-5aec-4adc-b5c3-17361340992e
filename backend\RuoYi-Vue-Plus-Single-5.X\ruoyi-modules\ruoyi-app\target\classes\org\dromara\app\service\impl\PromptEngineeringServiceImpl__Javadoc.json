{"doc": " Prompt工程服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildRagEnhancedPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String", "int"], "doc": " 构建RAG增强提示词\n\n @param userQuery        用户查询\n @param retrievalResults 检索结果\n @param agentType        Agent类型\n @param contextWindow    上下文窗口大小\n @return 增强后的提示词\n"}, {"name": "buildToolCallPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.util.List"], "doc": " 构建工具调用提示词\n\n @param userQuery           用户查询\n @param availableTools      可用工具列表\n @param conversationHistory 对话历史\n @return 工具调用提示词\n"}, {"name": "optimizeSystemPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 优化系统提示词\n\n @param originalPrompt 原始提示词\n @param agentType      Agent类型\n @param userContext    用户上下文\n @return 优化后的提示词\n"}, {"name": "buildConversationalPrompt", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": " 构建多轮对话提示词\n\n @param currentQuery        当前查询\n @param conversationHistory 对话历史\n @param maxHistoryLength    最大历史长度\n @return 多轮对话提示词\n"}, {"name": "buildChainOfThoughtPrompt", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建思维链提示词\n\n @param userQuery 用户查询\n @param taskType  任务类型\n @return 思维链提示词\n"}, {"name": "buildFewShotPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String"], "doc": " 构建少样本学习提示词\n\n @param userQuery       用户查询\n @param examples        示例列表\n @param taskDescription 任务描述\n @return 少样本学习提示词\n"}, {"name": "adjust<PERSON>rompt<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "int", "org.dromara.app.service.PromptEngineeringService.LengthAdjustmentStrategy"], "doc": " 动态调整提示词长度\n\n @param prompt    原始提示词\n @param maxTokens 最大token数\n @param priority  优先级策略\n @return 调整后的提示词\n"}, {"name": "extractKeyInformation", "paramTypes": ["java.lang.String", "int"], "doc": " 提取关键信息\n\n @param content   内容\n @param maxLength 最大长度\n @return 关键信息\n"}, {"name": "estimateTokenCount", "paramTypes": ["java.lang.String"], "doc": " 估计文本的token数量（简化版本）\n 注意：这是一个简化的估算方法，实际token数会因模型和分词器而异\n\n @param text 文本\n @return 估计的token数量\n"}, {"name": "truncateEnd", "paramTypes": ["java.lang.String", "int"], "doc": " 截断提示词末尾\n\n @param prompt    提示词\n @param maxTokens 最大token数\n @return 调整后的提示词\n"}, {"name": "truncateMiddle", "paramTypes": ["java.lang.String", "int"], "doc": " 截断提示词中间部分\n\n @param prompt    提示词\n @param maxTokens 最大token数\n @return 调整后的提示词\n"}, {"name": "compressContent", "paramTypes": ["java.lang.String", "int"], "doc": " 压缩内容（通过删除冗余信息）\n\n @param prompt    提示词\n @param maxTokens 最大token数\n @return 调整后的提示词\n"}, {"name": "prioritizeRecent", "paramTypes": ["java.lang.String", "int"], "doc": " 优先保留最近的内容\n\n @param prompt    提示词\n @param maxTokens 最大token数\n @return 调整后的提示词\n"}, {"name": "prioritizeRelevant", "paramTypes": ["java.lang.String", "int"], "doc": " 优先保留相关的内容\n\n @param prompt    提示词\n @param maxTokens 最大token数\n @return 调整后的提示词\n"}, {"name": "calculateParagraphRelevance", "paramTypes": ["java.lang.String"], "doc": " 计算段落相关性（简化版本）\n\n @param paragraph 段落\n @return 相关性分数\n"}, {"name": "calculateSentenceImportance", "paramTypes": ["java.lang.String"], "doc": " 计算句子重要性（简化版本）\n\n @param sentence 句子\n @return 重要性分数\n"}, {"name": "trimConversationHistory", "paramTypes": ["java.util.List", "int"], "doc": " 裁剪对话历史，保留最近和最相关的对话\n\n @param history   完整对话历史\n @param maxLength 最大保留长度\n @return 裁剪后的对话历史\n"}], "constructors": []}