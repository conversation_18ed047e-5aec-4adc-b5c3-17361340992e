{"doc": " 用户成长档案Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectProfileByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询成长档案\n\n @param userId 用户ID\n @return 成长档案\n"}, {"name": "updateProfileByUserId", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile"], "doc": " 根据用户ID更新成长档案\n\n @param profile 成长档案\n @return 更新数量\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID删除成长档案\n\n @param userId 用户ID\n @return 删除数量\n"}, {"name": "selectProfilesByCurrentStage", "paramTypes": ["java.lang.String"], "doc": " 根据当前阶段查询用户列表\n\n @param currentStage 当前阶段\n @return 用户成长档案列表\n"}, {"name": "selectProfilesStatistics", "paramTypes": [], "doc": " 查询所有用户的成长档案统计信息\n\n @return 成长档案统计信息\n"}, {"name": "updateLastActiveTimeByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID更新最后活跃时间\n\n @param userId 用户ID\n @return 更新数量\n"}, {"name": "incrementInterviewCountByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID增加面试次数\n\n @param userId    用户ID\n @param increment 增加数量\n @return 更新数量\n"}, {"name": "updateContinuousLearningDaysByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID更新连续学习天数\n\n @param userId 用户ID\n @param days   连续学习天数\n @return 更新数量\n"}, {"name": "incrementCompletedCoursesByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID增加完成课程数\n\n @param userId    用户ID\n @param increment 增加数量\n @return 更新数量\n"}], "constructors": []}