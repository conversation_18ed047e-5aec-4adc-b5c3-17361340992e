{"doc": " 面试结果Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": " 根据会话ID查询面试结果\n\n @param sessionId 会话ID\n @return 面试结果\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据用户ID查询面试结果列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 面试结果列表\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和状态查询面试结果列表\n\n @param userId 用户ID\n @param status 状态\n @return 面试结果列表\n"}, {"name": "selectStatsByJobId", "paramTypes": ["java.lang.Long"], "doc": " 根据岗位ID查询面试结果统计\n\n @param jobId 岗位ID\n @return 统计数据\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近的面试结果\n\n @param userId 用户ID\n @param days   天数\n @return 面试结果列表\n"}, {"name": "selectByScoreRange", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 根据分数范围查询面试结果\n\n @param minScore 最低分数\n @param maxScore 最高分数\n @return 面试结果列表\n"}, {"name": "selectAvgScoreByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户平均分数\n\n @param userId 用户ID\n @return 平均分数\n"}, {"name": "selectCountByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户面试次数\n\n @param userId 用户ID\n @return 面试次数\n"}, {"name": "selectHistoryPageWithJobInfo", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 分页查询用户面试历史记录（包含岗位和分类信息）\n\n @param page     分页参数\n @param userId   用户ID\n @param category 分类名称（可选）\n @param status   状态（可选）\n @return 面试结果列表\n"}], "constructors": []}