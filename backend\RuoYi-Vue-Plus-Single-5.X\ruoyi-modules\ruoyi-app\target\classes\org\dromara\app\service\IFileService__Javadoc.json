{"doc": " 文件服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "uploadChatFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.Long"], "doc": " 上传聊天附件\n\n @param file   文件\n @param type   文件类型：image/file/voice\n @param userId 用户ID\n @return 上传结果\n"}, {"name": "speechToText", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": " 语音转文字\n\n @param audioFile 音频文件\n @param userId    用户ID\n @return 转换结果\n"}, {"name": "deleteFile", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 删除文件\n\n @param fileUrl 文件URL\n @param userId  用户ID\n @return 是否成功\n"}, {"name": "getFileInfo", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取文件信息\n\n @param fileUrl 文件URL\n @param userId  用户ID\n @return 文件信息\n"}, {"name": "batchDeleteFiles", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量删除文件\n\n @param fileUrls 文件URL列表\n @param userId   用户ID\n @return 删除结果\n"}, {"name": "getFileUsageStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户文件使用统计\n\n @param userId 用户ID\n @return 统计信息\n"}], "constructors": []}