{"doc": " 活动类型枚举\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "code", "doc": " 活动类型代码\n"}, {"name": "description", "doc": " 活动类型描述\n"}], "enumConstants": [{"name": "COURSE", "doc": " 课程学习\n"}, {"name": "INTERVIEW", "doc": " 面试练习\n"}, {"name": "BOOK", "doc": " 书籍阅读\n"}, {"name": "VIDEO", "doc": " 视频学习\n"}, {"name": "EXERCISE", "doc": " 习题练习\n"}, {"name": "DOCUMENT", "doc": " 文档阅读\n"}, {"name": "OTHER", "doc": " 其他活动\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": " 根据代码获取活动类型\n\n @param code 活动类型代码\n @return 活动类型枚举\n"}, {"name": "isValidCode", "paramTypes": ["java.lang.String"], "doc": " 检查代码是否有效\n\n @param code 活动类型代码\n @return 是否有效\n"}], "constructors": []}